// Main package exports
export { ContentBuilder } from './components/ContentBuilder';
export { useContentBuilder } from './hooks/useContentBuilder';
export { useDragDrop } from './hooks/useDragDrop';

// Utility exports
export { generateHtml, HtmlGenerator } from './utils/htmlGenerator';

// Component exports
export { Canvas } from './components/Canvas/Canvas';
export { Row } from './components/Canvas/Row';
export { Column } from './components/Canvas/Column';
export { DropZone } from './components/Canvas/DropZone';

export { BlockPalette } from './components/Sidebar/BlockPalette';

export { TextBlock } from './components/Blocks/TextBlock';
export { ImageBlock } from './components/Blocks/ImageBlock';
export { ButtonBlock } from './components/Blocks/ButtonBlock';

export { Button } from './components/UI/Button';
export { Input } from './components/UI/Input';
export { ColorPicker } from './components/UI/ColorPicker';
export { Slider } from './components/UI/Slider';

// Type exports
export * from './types';

// Default export
export { ContentBuilder as default } from './components/ContentBuilder';
