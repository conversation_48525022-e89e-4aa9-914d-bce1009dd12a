import React, { createContext, useContext } from 'react';
import { ContentState, Block } from '../types';

export interface ContentBuilderContextValue {
  // Content state
  content: ContentState;
  selectedBlockId: string | null;
  isDirty: boolean;

  // Viewport and zoom
  viewport: 'mobile' | 'tablet' | 'desktop';
  zoom: number;

  // Content manipulation
  addRow: (index?: number) => void;
  removeRow: (rowId: string) => void;
  addColumn: (rowId: string, size: number, index?: number) => void;
  removeColumn: (columnId: string) => void;
  addBlock: (columnId: string, blockType: string, index?: number) => void;
  removeBlock: (blockId: string) => void;
  updateBlock: (blockId: string, updates: Partial<Block>) => void;
  selectBlock: (blockId: string | null) => void;

  // UI state
  setViewport: (viewport: 'mobile' | 'tablet' | 'desktop') => void;
  setZoom: (zoom: number) => void;
}

const ContentBuilderContext = createContext<ContentBuilderContextValue | null>(null);

export interface ContentBuilderProviderProps {
  value: ContentBuilderContextValue;
  children: React.ReactNode;
}

export const ContentBuilderProvider: React.FC<ContentBuilderProviderProps> = ({
  value,
  children,
}) => {
  return (
    <ContentBuilderContext.Provider value={value}>
      {children}
    </ContentBuilderContext.Provider>
  );
};

export const useContentBuilderContext = (): ContentBuilderContextValue => {
  const context = useContext(ContentBuilderContext);
  if (!context) {
    throw new Error('useContentBuilderContext must be used within a ContentBuilderProvider');
  }
  return context;
};

// Convenience hooks for specific parts of the context
export const useContentState = () => {
  const { content, selectedBlockId, isDirty } = useContentBuilderContext();
  return { content, selectedBlockId, isDirty };
};

export const useContentActions = () => {
  const {
    addRow,
    removeRow,
    addColumn,
    removeColumn,
    addBlock,
    removeBlock,
    updateBlock,
    selectBlock,
  } = useContentBuilderContext();

  return {
    addRow,
    removeRow,
    addColumn,
    removeColumn,
    addBlock,
    removeBlock,
    updateBlock,
    selectBlock,
  };
};

export const useViewportState = () => {
  const { viewport, zoom, setViewport, setZoom } = useContentBuilderContext();
  return { viewport, zoom, setViewport, setZoom };
};