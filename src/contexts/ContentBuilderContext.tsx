import { createContext, useContext } from 'react';
import { ContentState, Block } from '../types';

interface ContentBuilderContextValue {
  content: ContentState;
  selectedBlockId: string | null;
  isDirty: boolean;
  viewport: 'mobile' | 'tablet' | 'desktop';
  zoom: number;
  
  // Actions
  addRow: (index?: number) => void;
  removeRow: (rowId: string) => void;
  addColumn: (rowId: string, size: number, index?: number) => void;
  removeColumn: (columnId: string) => void;
  addBlock: (columnId: string, blockType: string, index?: number) => void;
  removeBlock: (blockId: string) => void;
  updateBlock: (blockId: string, updates: Partial<Block>) => void;
  selectBlock: (blockId: string | null) => void;
  setViewport: (viewport: 'mobile' | 'tablet' | 'desktop') => void;
  setZoom: (zoom: number) => void;
}

const ContentBuilderContext = createContext<ContentBuilderContextValue | null>(null);

export const useContentBuilderContext = () => {
  const context = useContext(ContentBuilderContext);
  if (!context) {
    throw new Error('useContentBuilderContext must be used within a ContentBuilderProvider');
  }
  return context;
};

export const ContentBuilderProvider = ContentBuilderContext.Provider;