import React, { useState } from 'react';
import { clsx } from 'clsx';
import { Block, ImageBlockContent } from '../../types';

export interface ImageBlockProps {
  block: Block;
  isSelected?: boolean;
  onUpdate?: (updates: Partial<Block>) => void;
  onSelect?: () => void;
}

export const ImageBlock: React.FC<ImageBlockProps> = ({
  block,
  isSelected = false,
  onUpdate,
  onSelect,
}) => {
  const [imageError, setImageError] = useState(false);
  const [showUrlInput, setShowUrlInput] = useState(false);
  const [tempUrl, setTempUrl] = useState('');

  const content: ImageBlockContent = block.content || {
    src: '',
    alt: 'Image',
    caption: '',
  };

  const handleImageError = () => {
    setImageError(true);
  };

  const handleUrlSubmit = () => {
    onUpdate?.({
      content: {
        ...content,
        src: tempUrl,
      },
    });
    setTempUrl('');
    setShowUrlInput(false);
    setImageError(false);
  };

  const handleFileUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (event) => {
        const result = event.target?.result as string;
        onUpdate?.({
          content: {
            ...content,
            src: result,
          },
        });
        setImageError(false);
      };
      reader.readAsDataURL(file);
    }
  };

  const styles = {
    width: block.styles?.width || '100%',
    height: block.styles?.height || 'auto',
    borderRadius: block.styles?.borderRadius,
    boxShadow: block.styles?.boxShadow,
    padding: block.styles?.padding,
    margin: block.styles?.margin,
  };

  return (
    <div
      className={clsx(
        'relative group cursor-pointer transition-all duration-200',
        isSelected && 'ring-2 ring-blue-500 ring-offset-2'
      )}
      onClick={onSelect}
    >
      {!content.src || imageError ? (
        <div className="flex flex-col items-center justify-center p-8 bg-gray-100 border-2 border-dashed border-gray-300 rounded-lg">
          <svg
            className="w-12 h-12 text-gray-400 mb-4"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
            />
          </svg>
          <p className="text-gray-500 text-sm mb-4">Click to add image</p>
          
          {showUrlInput ? (
            <div className="flex flex-col space-y-2 w-full max-w-xs">
              <input
                type="url"
                placeholder="Enter image URL"
                value={tempUrl}
                onChange={(e) => setTempUrl(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded text-sm"
                autoFocus
              />
              <div className="flex space-x-2">
                <button
                  onClick={handleUrlSubmit}
                  className="px-3 py-1 bg-blue-500 text-white text-sm rounded hover:bg-blue-600"
                >
                  Add
                </button>
                <button
                  onClick={() => setShowUrlInput(false)}
                  className="px-3 py-1 bg-gray-300 text-gray-700 text-sm rounded hover:bg-gray-400"
                >
                  Cancel
                </button>
              </div>
            </div>
          ) : (
            <div className="flex space-x-2">
              <label className="px-4 py-2 bg-blue-500 text-white text-sm rounded cursor-pointer hover:bg-blue-600">
                Upload File
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleFileUpload}
                  className="hidden"
                />
              </label>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  setShowUrlInput(true);
                }}
                className="px-4 py-2 bg-gray-500 text-white text-sm rounded hover:bg-gray-600"
              >
                Add URL
              </button>
            </div>
          )}
        </div>
      ) : (
        <div>
          <img
            src={content.src}
            alt={content.alt}
            style={styles}
            onError={handleImageError}
            className="max-w-full h-auto"
          />
          {content.caption && (
            <p className="text-sm text-gray-600 mt-2 text-center">
              {content.caption}
            </p>
          )}
        </div>
      )}

      {isSelected && content.src && !imageError && (
        <div className="absolute -top-8 left-0 flex space-x-1 bg-white border border-gray-200 rounded-md shadow-lg p-1">
          <button
            onClick={(e) => {
              e.stopPropagation();
              setShowUrlInput(true);
            }}
            className="px-2 py-1 text-xs hover:bg-gray-100 rounded"
          >
            Change URL
          </button>
          <label className="px-2 py-1 text-xs hover:bg-gray-100 rounded cursor-pointer">
            Upload New
            <input
              type="file"
              accept="image/*"
              onChange={handleFileUpload}
              className="hidden"
            />
          </label>
        </div>
      )}
    </div>
  );
};
