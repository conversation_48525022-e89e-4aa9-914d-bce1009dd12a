import React, { useState } from 'react';
import { clsx } from 'clsx';
import { Block, ButtonBlockContent } from '../../types';

export interface ButtonBlockProps {
  block: Block;
  isSelected?: boolean;
  onUpdate?: (updates: Partial<Block>) => void;
  onSelect?: () => void;
}

export const ButtonBlock: React.FC<ButtonBlockProps> = ({
  block,
  isSelected = false,
  onUpdate,
  onSelect,
}) => {
  const [isEditing, setIsEditing] = useState(false);
  const [tempContent, setTempContent] = useState<ButtonBlockContent>(
    block.content || {
      text: 'Click me',
      link: '',
      target: '_self',
      variant: 'primary',
    }
  );

  const handleClick = (e: React.MouseEvent) => {
    e.preventDefault();
    onSelect?.();
    if (!isSelected) {
      setIsEditing(true);
    }
  };

  const handleSave = () => {
    onUpdate?.({ content: tempContent });
    setIsEditing(false);
  };

  const handleCancel = () => {
    setTempContent(block.content || tempContent);
    setIsEditing(false);
  };

  const getButtonClasses = (variant: string) => {
    const baseClasses = 'inline-flex items-center px-4 py-2 text-sm font-medium rounded-md transition-colors cursor-pointer';
    
    switch (variant) {
      case 'primary':
        return `${baseClasses} bg-blue-600 text-white hover:bg-blue-700`;
      case 'secondary':
        return `${baseClasses} bg-gray-600 text-white hover:bg-gray-700`;
      case 'outline':
        return `${baseClasses} border border-gray-300 bg-white text-gray-700 hover:bg-gray-50`;
      case 'ghost':
        return `${baseClasses} text-gray-700 hover:bg-gray-100`;
      default:
        return `${baseClasses} bg-blue-600 text-white hover:bg-blue-700`;
    }
  };

  const styles = {
    fontSize: block.styles?.fontSize,
    fontWeight: block.styles?.fontWeight,
    fontFamily: block.styles?.fontFamily,
    padding: block.styles?.padding,
    margin: block.styles?.margin,
    borderRadius: block.styles?.borderRadius,
    backgroundColor: block.styles?.backgroundColor,
    color: block.styles?.color,
  };

  return (
    <div
      className={clsx(
        'relative group transition-all duration-200',
        isSelected && 'ring-2 ring-blue-500 ring-offset-2'
      )}
    >
      {isEditing ? (
        <div className="p-4 bg-white border border-gray-200 rounded-lg shadow-lg">
          <div className="space-y-3">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Button Text
              </label>
              <input
                type="text"
                value={tempContent.text}
                onChange={(e) => setTempContent({ ...tempContent, text: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                placeholder="Button text"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Link URL (optional)
              </label>
              <input
                type="url"
                value={tempContent.link || ''}
                onChange={(e) => setTempContent({ ...tempContent, link: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                placeholder="https://example.com"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Style
              </label>
              <select
                value={tempContent.variant}
                onChange={(e) => setTempContent({ ...tempContent, variant: e.target.value as any })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
              >
                <option value="primary">Primary</option>
                <option value="secondary">Secondary</option>
                <option value="outline">Outline</option>
                <option value="ghost">Ghost</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Target
              </label>
              <select
                value={tempContent.target}
                onChange={(e) => setTempContent({ ...tempContent, target: e.target.value as any })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
              >
                <option value="_self">Same window</option>
                <option value="_blank">New window</option>
              </select>
            </div>
            
            <div className="flex space-x-2 pt-2">
              <button
                onClick={handleSave}
                className="px-3 py-1 bg-blue-500 text-white text-sm rounded hover:bg-blue-600"
              >
                Save
              </button>
              <button
                onClick={handleCancel}
                className="px-3 py-1 bg-gray-300 text-gray-700 text-sm rounded hover:bg-gray-400"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      ) : (
        <div
          onClick={handleClick}
          className={clsx(
            getButtonClasses(tempContent.variant),
            'select-none'
          )}
          style={styles}
        >
          {tempContent.text}
        </div>
      )}
    </div>
  );
};
