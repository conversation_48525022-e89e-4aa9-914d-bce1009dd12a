import React from 'react';
import { clsx } from 'clsx';
import { Block, DividerBlockContent } from '../../types';

export interface DividerBlockProps {
  block: Block;
  isSelected?: boolean;
  onUpdate?: (updates: Partial<Block>) => void;
  onSelect?: () => void;
}

export const DividerBlock: React.FC<DividerBlockProps> = ({
  block,
  isSelected = false,
  onUpdate,
  onSelect,
}) => {
  const content: DividerBlockContent = block.content || {
    style: 'solid',
    thickness: 1,
    color: '#e5e7eb',
  };

  const styles = {
    margin: block.styles?.margin || '20px 0',
    ...block.styles?.customCss && { cssText: block.styles.customCss },
  };

  const dividerStyles = {
    borderTop: `${content.thickness}px ${content.style} ${content.color}`,
    width: '100%',
  };

  return (
    <div
      className={clsx(
        'relative group cursor-pointer transition-all duration-200',
        isSelected && 'ring-2 ring-blue-500 ring-offset-2'
      )}
      onClick={onSelect}
      style={styles}
    >
      <hr style={dividerStyles} className="border-0" />
      
      {isSelected && (
        <div className="absolute -top-8 left-0 flex space-x-1 bg-white border border-gray-200 rounded-md shadow-lg p-1">
          <button
            onClick={(e) => {
              e.stopPropagation();
              onUpdate?.({
                content: { ...content, style: 'solid' }
              });
            }}
            className={clsx(
              'px-2 py-1 text-xs rounded',
              content.style === 'solid' ? 'bg-blue-100 text-blue-700' : 'hover:bg-gray-100'
            )}
          >
            Solid
          </button>
          <button
            onClick={(e) => {
              e.stopPropagation();
              onUpdate?.({
                content: { ...content, style: 'dashed' }
              });
            }}
            className={clsx(
              'px-2 py-1 text-xs rounded',
              content.style === 'dashed' ? 'bg-blue-100 text-blue-700' : 'hover:bg-gray-100'
            )}
          >
            Dashed
          </button>
          <button
            onClick={(e) => {
              e.stopPropagation();
              onUpdate?.({
                content: { ...content, style: 'dotted' }
              });
            }}
            className={clsx(
              'px-2 py-1 text-xs rounded',
              content.style === 'dotted' ? 'bg-blue-100 text-blue-700' : 'hover:bg-gray-100'
            )}
          >
            Dotted
          </button>
        </div>
      )}
    </div>
  );
};
