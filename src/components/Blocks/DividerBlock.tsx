import React from 'react';
import { clsx } from 'clsx';
import { Block, DividerBlockContent } from '../../types';

export interface DividerBlockProps {
  block: Block;
  isSelected?: boolean;
  onUpdate?: (updates: Partial<Block>) => void;
  onSelect?: () => void;
}

export const DividerBlock: React.FC<DividerBlockProps> = ({
  block,
  isSelected = false,
  onUpdate,
  onSelect,
}) => {
  const content: DividerBlockContent = block.content || {
    style: 'solid',
    thickness: 1,
    color: '#e5e7eb',
  };

  const styles = {
    margin: block.styles?.margin || '20px 0',
    ...block.styles?.customCss && { cssText: block.styles.customCss },
  };

  const dividerStyles = {
    borderTop: `${content.thickness}px ${content.style} ${content.color}`,
    width: '100%',
  };

  return (
    <div
      className={clsx(
        'relative group cursor-pointer transition-all duration-300 py-4',
        'hover:bg-gray-50/50 rounded-lg',
        isSelected && 'ring-2 ring-blue-500 ring-offset-2 bg-blue-50/30'
      )}
      onClick={onSelect}
      style={styles}
    >
      {/* Divider with enhanced styling */}
      <div className="relative">
        <hr
          style={dividerStyles}
          className={clsx(
            "border-0 transition-all duration-300",
            isSelected && "shadow-sm"
          )}
        />

        {/* Hover indicator */}
        <div className={clsx(
          "absolute inset-0 bg-gradient-to-r from-transparent via-blue-200 to-transparent opacity-0 group-hover:opacity-30 transition-opacity duration-300",
          isSelected && "opacity-20"
        )} />
      </div>

      {/* Selection controls */}
      {isSelected && (
        <div className="absolute -top-12 left-1/2 transform -translate-x-1/2 flex space-x-1 bg-white border border-gray-200 rounded-lg shadow-xl p-2 z-10">
          <div className="flex items-center space-x-2">
            <span className="text-xs font-medium text-gray-600">Style:</span>
            <div className="flex space-x-1">
              {[
                { key: 'solid', label: 'Solid', icon: '━' },
                { key: 'dashed', label: 'Dashed', icon: '┅' },
                { key: 'dotted', label: 'Dotted', icon: '┈' }
              ].map(({ key, label, icon }) => (
                <button
                  key={key}
                  onClick={(e) => {
                    e.stopPropagation();
                    onUpdate?.({
                      content: { ...content, style: key }
                    });
                  }}
                  className={clsx(
                    'px-3 py-1.5 text-xs rounded-md transition-all duration-200 font-medium flex items-center space-x-1',
                    content.style === key
                      ? 'bg-blue-500 text-white shadow-sm'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  )}
                  title={label}
                >
                  <span className="font-mono">{icon}</span>
                  <span>{label}</span>
                </button>
              ))}
            </div>
          </div>

          {/* Thickness control */}
          <div className="flex items-center space-x-2 border-l border-gray-200 pl-2">
            <span className="text-xs font-medium text-gray-600">Width:</span>
            <div className="flex space-x-1">
              {[1, 2, 3, 4].map((thickness) => (
                <button
                  key={thickness}
                  onClick={(e) => {
                    e.stopPropagation();
                    onUpdate?.({
                      content: { ...content, thickness }
                    });
                  }}
                  className={clsx(
                    'w-6 h-6 rounded text-xs transition-all duration-200 font-medium',
                    content.thickness === thickness
                      ? 'bg-blue-500 text-white shadow-sm'
                      : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                  )}
                  title={`${thickness}px`}
                >
                  {thickness}
                </button>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Hover hint */}
      {!isSelected && (
        <div className="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
          <div className="bg-black/75 text-white text-xs px-2 py-1 rounded-md">
            Click to customize divider
          </div>
        </div>
      )}
    </div>
  );
};
