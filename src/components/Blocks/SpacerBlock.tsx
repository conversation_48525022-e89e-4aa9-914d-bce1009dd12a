import React from 'react';
import { clsx } from 'clsx';
import { Block, SpacerBlockContent } from '../../types';

export interface SpacerBlockProps {
  block: Block;
  isSelected?: boolean;
  onUpdate?: (updates: Partial<Block>) => void;
  onSelect?: () => void;
}

export const SpacerBlock: React.FC<SpacerBlockProps> = ({
  block,
  isSelected = false,
  onUpdate,
  onSelect,
}) => {
  const content: SpacerBlockContent = block.content || {
    height: 50,
  };

  const styles = {
    height: `${content.height}px`,
    ...block.styles?.customCss && { cssText: block.styles.customCss },
  };

  return (
    <div
      className={clsx(
        'relative group cursor-pointer transition-all duration-200 bg-transparent',
        isSelected && 'ring-2 ring-blue-500 ring-offset-2',
        isSelected ? 'bg-blue-50' : 'hover:bg-gray-50'
      )}
      onClick={onSelect}
      style={styles}
    >
      {isSelected && (
        <>
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="flex items-center space-x-2 bg-white border border-gray-200 rounded-md shadow-lg px-3 py-2">
              <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" />
              </svg>
              <span className="text-sm text-gray-600">Spacer - {content.height}px</span>
            </div>
          </div>
          
          <div className="absolute -top-8 left-0 flex items-center space-x-2 bg-white border border-gray-200 rounded-md shadow-lg px-2 py-1">
            <input
              type="range"
              min="10"
              max="200"
              value={content.height}
              onChange={(e) => {
                const height = parseInt(e.target.value);
                onUpdate?.({
                  content: { ...content, height }
                });
              }}
              className="w-20"
            />
            <input
              type="number"
              min="10"
              max="500"
              value={content.height}
              onChange={(e) => {
                const height = parseInt(e.target.value) || 50;
                onUpdate?.({
                  content: { ...content, height }
                });
              }}
              className="w-16 px-2 py-1 text-xs border border-gray-300 rounded"
            />
            <span className="text-xs text-gray-500">px</span>
          </div>
        </>
      )}
    </div>
  );
};
