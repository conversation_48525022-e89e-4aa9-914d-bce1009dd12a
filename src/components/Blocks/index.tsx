/* eslint-disable react-refresh/only-export-components */
import React from 'react';
import { BlockType } from '../../types';
import { TextBlock } from './TextBlock';
import { ImageBlock } from './ImageBlock';
import { ButtonBlock } from './ButtonBlock';
import { DividerBlock } from './DividerBlock';
import { SpacerBlock } from './SpacerBlock';

// Block type icons (using SVG icons)
const TextIcon: React.FC = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h7" />
  </svg>
);

const ImageIcon: React.FC = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
  </svg>
);

const ButtonIcon: React.FC = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122" />
  </svg>
);

const DividerIcon: React.FC = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
  </svg>
);

const SpacerIcon: React.FC = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" />
  </svg>
);

// Default block types
export const defaultBlockTypes: Record<string, BlockType> = {
  text: {
    name: 'Text',
    category: 'Content',
    icon: TextIcon,
    defaultProps: {
      id: '',
      type: 'text',
      content: {
        text: 'Your text here...',
        tag: 'p',
      },
      styles: {
        fontSize: '16px',
        color: '#000000',
        textAlign: 'left',
      },
      attributes: {},
    },
    editable: true,
    resizable: false,
    configurable: true,
  },
  heading: {
    name: 'Heading',
    category: 'Content',
    icon: TextIcon,
    defaultProps: {
      id: '',
      type: 'text',
      content: {
        text: 'Your heading here',
        tag: 'h2',
      },
      styles: {
        fontSize: '32px',
        fontWeight: 'bold',
        color: '#000000',
        textAlign: 'left',
      },
      attributes: {},
    },
    editable: true,
    resizable: false,
    configurable: true,
  },
  image: {
    name: 'Image',
    category: 'Media',
    icon: ImageIcon,
    defaultProps: {
      id: '',
      type: 'image',
      content: {
        src: '',
        alt: 'Image',
        caption: '',
      },
      styles: {
        width: '100%',
        height: 'auto',
      },
      attributes: {},
    },
    editable: true,
    resizable: true,
    configurable: true,
  },
  button: {
    name: 'Button',
    category: 'Interactive',
    icon: ButtonIcon,
    defaultProps: {
      id: '',
      type: 'button',
      content: {
        text: 'Click me',
        link: '',
        target: '_self',
        variant: 'primary',
      },
      styles: {},
      attributes: {},
    },
    editable: true,
    resizable: false,
    configurable: true,
  },
  divider: {
    name: 'Divider',
    category: 'Layout',
    icon: DividerIcon,
    defaultProps: {
      id: '',
      type: 'divider',
      content: {
        style: 'solid',
        thickness: 1,
        color: '#e5e7eb',
      },
      styles: {
        margin: '20px 0',
      },
      attributes: {},
    },
    editable: false,
    resizable: false,
    configurable: true,
  },
  spacer: {
    name: 'Spacer',
    category: 'Layout',
    icon: SpacerIcon,
    defaultProps: {
      id: '',
      type: 'spacer',
      content: {
        height: 50,
      },
      styles: {},
      attributes: {},
    },
    editable: false,
    resizable: true,
    configurable: true,
  },
};

// Block component registry
export const blockComponents: Record<string, React.ComponentType<any>> = {
  text: TextBlock,
  image: ImageBlock,
  button: ButtonBlock,
  divider: DividerBlock,
  spacer: SpacerBlock,
};

// Block registry class for managing custom blocks
export class BlockRegistry {
  private blockTypes: Record<string, BlockType> = { ...defaultBlockTypes };
  private components: Record<string, React.ComponentType<any>> = { ...blockComponents };

  registerBlockType(type: string, blockType: BlockType, component: React.ComponentType<any>) {
    this.blockTypes[type] = blockType;
    this.components[type] = component;
  }

  unregisterBlockType(type: string) {
    delete this.blockTypes[type];
    delete this.components[type];
  }

  getBlockType(type: string): BlockType | undefined {
    return this.blockTypes[type];
  }

  getBlockComponent(type: string): React.ComponentType<any> | undefined {
    return this.components[type];
  }

  getAllBlockTypes(): Record<string, BlockType> {
    return { ...this.blockTypes };
  }

  getBlockTypesByCategory(category: string): Record<string, BlockType> {
    return Object.entries(this.blockTypes)
      .filter(([, blockType]) => blockType.category === category)
      .reduce((acc, [key, blockType]) => ({ ...acc, [key]: blockType }), {});
  }

  getCategories(): string[] {
    const categories = new Set(Object.values(this.blockTypes).map(bt => bt.category));
    return Array.from(categories);
  }
}

// Global block registry instance
export const blockRegistry = new BlockRegistry();