import React, { useState, useRef, useEffect } from 'react';
import { clsx } from 'clsx';
import { Block, TextBlockContent } from '../../types';

export interface TextBlockProps {
  block: Block;
  isSelected?: boolean;
  isEditing?: boolean;
  onUpdate?: (updates: Partial<Block>) => void;
  onSelect?: () => void;
  onStartEdit?: () => void;
  onEndEdit?: () => void;
}

export const TextBlock: React.FC<TextBlockProps> = ({
  block,
  isSelected = false,
  isEditing = false,
  onUpdate,
  onSelect,
  onStartEdit,
  onEndEdit,
}) => {
  const [localContent, setLocalContent] = useState<TextBlockContent>(
    block.content || { text: 'Click to edit text', tag: 'p' }
  );
  const [isInternalEditing, setIsInternalEditing] = useState(false);
  const textRef = useRef<HTMLDivElement>(null);

  const editing = isEditing || isInternalEditing;

  useEffect(() => {
    if (editing && textRef.current) {
      textRef.current.focus();
      // Place cursor at end
      const range = document.createRange();
      const selection = window.getSelection();
      range.selectNodeContents(textRef.current);
      range.collapse(false);
      selection?.removeAllRanges();
      selection?.addRange(range);
    }
  }, [editing]);

  const handleClick = () => {
    onSelect?.();
    if (!editing) {
      setIsInternalEditing(true);
      onStartEdit?.();
    }
  };

  const handleBlur = () => {
    setIsInternalEditing(false);
    onEndEdit?.();
    onUpdate?.({
      content: localContent,
    });
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleBlur();
    }
    if (e.key === 'Escape') {
      handleBlur();
    }
  };

  const handleInput = () => {
    if (textRef.current) {
      setLocalContent({
        ...localContent,
        text: textRef.current.innerText,
      });
    }
  };

  const styles = {
    fontSize: block.styles?.fontSize,
    fontWeight: block.styles?.fontWeight,
    fontFamily: block.styles?.fontFamily,
    color: block.styles?.color,
    textAlign: block.styles?.textAlign as 'left' | 'center' | 'right' | 'justify' | undefined,
    lineHeight: block.styles?.lineHeight,
    padding: block.styles?.padding,
    margin: block.styles?.margin,
    ...block.styles?.customCss && { cssText: block.styles.customCss },
  };

  const renderTextElement = () => {
    const commonProps = {
      ref: textRef,
      contentEditable: editing,
      suppressContentEditableWarning: true,
      onBlur: handleBlur,
      onKeyDown: handleKeyDown,
      onInput: handleInput,
      style: styles,
      className: clsx(
        'outline-none',
        !editing && 'hover:bg-gray-50',
        editing && 'bg-white'
      ),
      children: localContent.text,
    };

    switch (localContent.tag) {
      case 'h1':
        return <h1 {...commonProps} />;
      case 'h2':
        return <h2 {...commonProps} />;
      case 'h3':
        return <h3 {...commonProps} />;
      case 'h4':
        return <h4 {...commonProps} />;
      case 'h5':
        return <h5 {...commonProps} />;
      case 'h6':
        return <h6 {...commonProps} />;
      case 'span':
        return <span {...commonProps} />;
      case 'div':
        return <div {...commonProps} />;
      default:
        return <p {...commonProps} />;
    }
  };

  return (
    <div
      className={clsx(
        'relative group cursor-pointer transition-all duration-200',
        isSelected && 'ring-2 ring-blue-500 ring-offset-2',
        editing && 'ring-2 ring-green-500 ring-offset-2'
      )}
      onClick={handleClick}
    >
      {renderTextElement()}
      {renderTextElement()}

      {isSelected && !editing && (
        <div className="absolute -top-8 left-0 flex space-x-1 bg-white border border-gray-200 rounded-md shadow-lg p-1">
          <button
            onClick={(e) => {
              e.stopPropagation();
              setLocalContent({ ...localContent, tag: 'h1' });
              onUpdate?.({ content: { ...localContent, tag: 'h1' } });
            }}
            className="px-2 py-1 text-xs hover:bg-gray-100 rounded"
          >
            H1
          </button>
          <button
            onClick={(e) => {
              e.stopPropagation();
              setLocalContent({ ...localContent, tag: 'h2' });
              onUpdate?.({ content: { ...localContent, tag: 'h2' } });
            }}
            className="px-2 py-1 text-xs hover:bg-gray-100 rounded"
          >
            H2
          </button>
          <button
            onClick={(e) => {
              e.stopPropagation();
              setLocalContent({ ...localContent, tag: 'p' });
              onUpdate?.({ content: { ...localContent, tag: 'p' } });
            }}
            className="px-2 py-1 text-xs hover:bg-gray-100 rounded"
          >
            P
          </button>
        </div>
      )}
    </div>
  );
};
