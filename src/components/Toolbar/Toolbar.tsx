import React from 'react';
import { clsx } from 'clsx';
import { Button } from '../UI/Button';

export interface ToolbarProps {
  // History
  onUndo: () => void;
  canUndo: boolean;
  onRedo: () => void;
  canRedo: boolean;

  // Canvas Actions
  onAddRow: () => void;
  onClearContent: () => void;

  // Viewport
  currentViewport: 'mobile' | 'tablet' | 'desktop';
  onViewportChange: (viewport: 'mobile' | 'tablet' | 'desktop') => void;
  enableResponsivePreview: boolean;

  // Zoom
  currentZoom: number;
  onZoomChange: (zoom: number) => void;

  // Code & Export
  onShowCode: () => void;
  enableCodeExport: boolean;
  onSave: () => void;
  isDirty: boolean;
  onExport: () => void;

  theme?: 'light' | 'dark' | 'custom';
}

export const Toolbar: React.FC<ToolbarProps> = ({
  onUndo,
  canUndo,
  onRedo,
  canRedo,
  onAddRow,
  onClearContent,
  currentViewport,
  onViewportChange,
  enableResponsivePreview,
  currentZoom,
  onZoomChange,
  onShowCode,
  enableCodeExport,
  onSave,
  isDirty,
  onExport,
  theme = 'light',
}) => {
  const handleZoomIn = () => onZoomChange(Math.min(200, currentZoom + 25));
  const handleZoomOut = () => onZoomChange(Math.max(50, currentZoom - 25));

  // Use light theme UI for both 'light' and 'custom' themes
  const isDark = theme === 'dark';
  
  // Common style variables
  const iconClass = "w-3.5 h-3.5"; // Smaller icons for more compact UI
  const separatorClass = isDark ? "bg-gray-700" : "bg-gray-200";
  const actionButtonClass = clsx(
    "p-1 rounded transition-colors", 
    isDark ? "hover:bg-gray-700" : "hover:bg-gray-100"
  );
  
  return (
    <div
      className={clsx(
        'px-2 py-1 flex items-center justify-between shadow-sm z-40 border-b',
        isDark 
          ? 'bg-gray-800 border-gray-700 text-gray-200' 
          : 'bg-white border-gray-200 text-gray-700'
      )}
    >
      {/* Left Section: Brand & Core Actions */}
      <div className="flex items-center space-x-1.5">
        {/* Logo/Title */}
        <div className="flex items-center space-x-1.5 mr-1.5">
          <div className={clsx(
            "w-6 h-6 rounded-md flex items-center justify-center shadow-sm",
            isDark ? 'bg-gradient-to-br from-blue-600 to-blue-700' : 'bg-gradient-to-br from-blue-500 to-blue-600'
          )}>
            <svg className={clsx(iconClass, "text-white")} fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
            </svg>
          </div>
          <span className={clsx('text-sm font-semibold', isDark ? 'text-gray-100' : 'text-gray-800')}>
            Builder
          </span>
        </div>

        <div className={clsx("h-5 w-px", separatorClass)} />

        {/* History */}
        <button 
          className={clsx(actionButtonClass, !canUndo && "opacity-50 cursor-not-allowed")} 
          onClick={onUndo} 
          disabled={!canUndo} 
          title="Undo (Ctrl+Z)"
        >
          <svg className={iconClass} fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6" />
          </svg>
        </button>
        
        <button 
          className={clsx(actionButtonClass, !canRedo && "opacity-50 cursor-not-allowed")} 
          onClick={onRedo} 
          disabled={!canRedo} 
          title="Redo (Ctrl+Y)"
        >
          <svg className={iconClass} fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 10h-10a8 8 0 00-8 8v2m18-10l-6-6m6 6l-6 6" />
          </svg>
        </button>
        
        <div className={clsx("h-5 w-px", separatorClass)} />

        {/* Add Content */}
        <Button 
          variant="ghost" 
          size="sm" 
          onClick={onAddRow} 
          title="Add New Row" 
          className="text-xs py-1 px-2"
        >
          <svg className={clsx(iconClass, "mr-1")} fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
          </svg>
          Row
        </Button>
      </div>

      {/* Center Section: Viewport & Zoom */}
      <div className="flex items-center space-x-2">
        {enableResponsivePreview && (
          <div className={clsx(
            "flex items-center p-0.5 rounded-md", 
            isDark ? "bg-gray-700" : "bg-gray-100"
          )}>
            {(['desktop', 'tablet', 'mobile'] as const).map((size) => (
              <button
                key={size}
                onClick={() => onViewportChange(size)}
                title={`${size.charAt(0).toUpperCase() + size.slice(1)} view`}
                className={clsx(
                  "p-1 rounded transition-colors",
                  currentViewport === size 
                    ? (isDark ? "bg-blue-600 text-white" : "bg-blue-500 text-white") 
                    : (isDark ? "hover:bg-gray-600" : "hover:bg-gray-200")
                )}
              >
                {size === 'desktop' && <svg className={iconClass} fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" /></svg>}
                {size === 'tablet' && <svg className={iconClass} fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M8 21h8a1 1 0 001-1V4a1 1 0 00-1-1H8a1 1 0 00-1 1v16a1 1 0 001 1z" /></svg>}
                {size === 'mobile' && <svg className={iconClass} fill="none" stroke="currentColor" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 18h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" /></svg>}
              </button>
            ))}
          </div>
        )}

        <div className={clsx("h-5 w-px", separatorClass)} />
        
        {/* Zoom Controls */}
        <div className="flex items-center space-x-1">
          <button 
            className={clsx(
              actionButtonClass, 
              currentZoom <= 50 && "opacity-50 cursor-not-allowed"
            )} 
            onClick={handleZoomOut} 
            disabled={currentZoom <= 50} 
            title="Zoom Out"
          >
            <svg className={iconClass} fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM13 10H7" />
            </svg>
          </button>
          <span className="text-xs font-medium min-w-[2.5rem] text-center tabular-nums">{currentZoom}%</span>
          <button 
            className={clsx(
              actionButtonClass, 
              currentZoom >= 200 && "opacity-50 cursor-not-allowed"
            )} 
            onClick={handleZoomIn} 
            disabled={currentZoom >= 200} 
            title="Zoom In"
          >
            <svg className={iconClass} fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0zM13 10h7" />
            </svg>
          </button>
        </div>
      </div>

      {/* Right Section: Actions */}
      <div className="flex items-center space-x-1.5">
        {enableCodeExport && (
          <button 
            className={actionButtonClass} 
            onClick={onShowCode} 
            title="View Code"
          >
            <svg className={iconClass} fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" />
            </svg>
          </button>
        )}
        
        <button 
          className={clsx(
            actionButtonClass, 
            isDark ? "text-red-400 hover:text-red-300" : "text-red-500 hover:text-red-600"
          )} 
          onClick={onClearContent} 
          title="Clear All Content"
        >
          <svg className={iconClass} fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
          </svg>
        </button>
        
        <div className={clsx("h-5 w-px", separatorClass)} />

        <Button 
          variant="outline" 
          size="sm" 
          onClick={onSave} 
          disabled={!isDirty} 
          title="Save Content" 
          className={clsx(
            "py-1 px-2 text-xs", 
            isDark && "text-gray-200 border-gray-600 hover:bg-gray-700"
          )}
        >
          <svg className={clsx(iconClass, "mr-1")} fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12" />
          </svg>
          Save
        </Button>
        
        <Button 
          variant="primary" 
          size="sm" 
          onClick={onExport} 
          title="Export Content"
          className={clsx(
            "py-1 px-2 text-xs",
            isDark && "bg-blue-600 hover:bg-blue-700"
          )}
        >
          <svg className={clsx(iconClass, "mr-1")} fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3M3 17V7a2 2 0 012-2h6l2 2h6a2 2 0 012 2v10a2 2 0 01-2 2H5a2 2 0 01-2-2z" />
          </svg>
          Export
        </Button>
      </div>
    </div>
  );
};
