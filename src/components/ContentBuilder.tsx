import React, { useState } from 'react';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { clsx } from 'clsx';
import { ContentBuilderProps, Row, Block } from '../types';
import { useContentBuilder } from '../hooks/useContentBuilder';
import { Canvas } from './Canvas/Canvas';
import { BlockPalette } from './Sidebar/BlockPalette';
import { PropertiesPanel } from './Sidebar/PropertiesPanel';
import { Toolbar } from './Toolbar/Toolbar';
import { ContentBuilderProvider } from '../contexts/ContentBuilderContext';

// Default render functions
const DefaultToolbar: React.FC<any> = (props) => <Toolbar {...props} />;

const DefaultSidebar: React.FC<any> = ({ isOpen, onToggle, enabledBlocks }) => (
  <div className={clsx(
    'transition-all duration-300 bg-white border-r border-gray-200 flex flex-col',
    isOpen ? 'w-80' : 'w-0 overflow-hidden'
  )}>
    {isOpen && (
      <>
        <div className="p-4 border-b border-gray-200 flex items-center justify-between">
          <h2 className="text-lg font-semibold text-gray-900">Components</h2>
          <button
            onClick={onToggle}
            className="p-1 rounded hover:bg-gray-100 transition-colors"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        <div className="flex-1 overflow-hidden">
          <BlockPalette enabledBlocks={enabledBlocks} />
        </div>
      </>
    )}
  </div>
);

const DefaultPropertiesPanel: React.FC<any> = ({ isOpen, onToggle, selectedBlock, onUpdate, onDelete, onDuplicate }) => (
  <div className={clsx(
    'transition-all duration-300 bg-white border-l border-gray-200 flex flex-col',
    isOpen ? 'w-80' : 'w-0 overflow-hidden'
  )}>
    {isOpen && selectedBlock && (
      <>
        <div className="p-4 border-b border-gray-200 flex items-center justify-between">
          <h2 className="text-lg font-semibold text-gray-900">Properties</h2>
          <button
            onClick={onToggle}
            className="p-1 rounded hover:bg-gray-100 transition-colors"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        <div className="flex-1 overflow-y-auto p-4">
          <PropertiesPanel
            block={selectedBlock}
            onUpdate={onUpdate}
            onDelete={onDelete}
            onDuplicate={onDuplicate}
          />
        </div>
      </>
    )}
  </div>
);

const DefaultCanvas: React.FC<any> = (props) => <Canvas {...props} />;

export const ContentBuilder: React.FC<ContentBuilderProps> = ({
  initialContent,
  theme = 'light',
  className,
  style,
  showSidebar = true,
  showToolbar = true,
  showPropertiesPanel = true,
  enableResponsivePreview = true,
  enableCodeExport = true,
  maxColumns = 12,
  enabledBlocks,
  // customBlocks,
  onChange,
  onSave,
  onExport,
  onBlockSelect,
  // renderBlock,
  renderToolbar = DefaultToolbar,
  renderSidebar = DefaultSidebar,
  renderPropertiesPanel = DefaultPropertiesPanel,
  renderCanvas = DefaultCanvas,
}) => {
  const [viewport, setViewport] = useState<'mobile' | 'tablet' | 'desktop'>('desktop');
  const [zoom, setZoom] = useState(100);
  const [leftSidebarOpen, setLeftSidebarOpen] = useState(showSidebar);
  const [rightSidebarOpen, setRightSidebarOpen] = useState(showPropertiesPanel);
  const [showCode, setShowCode] = useState(false);

  const {
    content,
    selectedBlockId,
    isDirty,
    addRow,
    removeRow,
    addColumn,
    removeColumn,
    addBlock,
    removeBlock,
    updateBlock,
    selectBlock,
    duplicateBlock,
    moveBlock,
    clearContent,
    exportHtml,
    undo,
    redo,
    canUndo,
    canRedo,
  } = useContentBuilder({
    initialContent,
    maxColumns,
    onChange: (newContent) => {
      onChange?.(newContent);
      onBlockSelect?.(selectedBlockId);
    },
  });

  // Event handlers
  const handleSave = () => {
    onSave?.(content);
  };

  const handleExport = () => {
    const html = exportHtml();
    onExport?.(html, '/* Generated CSS */');
  };

  const handleAddRow = () => {
    addRow();
  };

  const handleClearContent = () => {
    if (window.confirm('Are you sure you want to clear all content?')) {
      clearContent();
    }
  };

  // Utility functions
  const getViewportWidth = () => {
    switch (viewport) {
      case 'mobile': return '375px';
      case 'tablet': return '768px';
      case 'desktop': return '100%';
      default: return '100%';
    }
  };

  const findBlockById = (blockId: string): Block | null => {
    const searchInRows = (rows: Row[]): Block | null => {
      for (const row of rows) {
        for (const column of row.columns) {
          const block = column.blocks.find(b => b.id === blockId);
          if (block) return block;
        }
      }
      return null;
    };
    return searchInRows(content.rows);
  };

  const selectedBlock = selectedBlockId ? findBlockById(selectedBlockId) : null;

  // Create context value
  const contextValue = {
    content,
    selectedBlockId,
    isDirty,
    viewport,
    zoom,
    addRow,
    removeRow,
    addColumn,
    removeColumn,
    addBlock,
    removeBlock,
    updateBlock,
    selectBlock,
    setViewport,
    setZoom,
  };

  // Handler functions for render props
  const onUndo = () => undo();
  const onRedo = () => redo();

  return (
    <DndProvider backend={HTML5Backend}>
      <ContentBuilderProvider value={contextValue}>
        <div
          className={clsx(
            'h-screen flex flex-col bg-gray-50',
            theme === 'dark' && 'bg-gray-900',
            className
          )}
          style={style}
        >
          {showToolbar && renderToolbar({
            onUndo, canUndo, onRedo, canRedo, 
            onAddRow: handleAddRow,
            onClearContent: handleClearContent,
            currentViewport: viewport,
            onViewportChange: setViewport,
            enableResponsivePreview,
            currentZoom: zoom,
            onZoomChange: setZoom,
            onShowCode: () => setShowCode(!showCode),
            enableCodeExport,
            onSave: handleSave,
            isDirty,
            onExport: handleExport,
            theme,
          })}
          
          <div className="flex-1 flex overflow-hidden">
            {showSidebar && renderSidebar({
              isOpen: leftSidebarOpen,
              onToggle: () => setLeftSidebarOpen(!leftSidebarOpen),
              enabledBlocks,
            })}
            
            {renderCanvas({
              content,
              selectedBlockId,
              onRowAdd: addRow,
              onRowRemove: removeRow,
              onColumnAdd: addColumn,
              onColumnRemove: removeColumn,
              onBlockAdd: addBlock,
              onBlockRemove: removeBlock,
              onBlockUpdate: updateBlock,
              onBlockSelect: selectBlock,
              onBlockMove: moveBlock,
              viewport,
              className: "h-full",
              zoom,
              getViewportWidth,
            })}
            
            {showPropertiesPanel && renderPropertiesPanel({
              isOpen: rightSidebarOpen && !!selectedBlockId,
              onToggle: () => setRightSidebarOpen(!rightSidebarOpen),
              selectedBlock,
              onUpdate: updateBlock,
              onDelete: removeBlock,
              onDuplicate: duplicateBlock,
            })}
          </div>
        </div>
      </ContentBuilderProvider>
    </DndProvider>
  );
};
