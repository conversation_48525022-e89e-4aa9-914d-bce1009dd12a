import React, { useCallback, useMemo, useRef } from 'react';
import { useDrop, DropTargetMonitor } from 'react-dnd';
import { clsx } from 'clsx';
import { ItemTypes, BlockTypeDragItem, BlockInstanceDragItem } from '../../hooks/useDragDrop';

export interface DropZoneProps {
  onDrop: (blockType: string) => void;
  onBlockMove?: (blockId: string, sourceColumnId: string, sourceIndex: number) => void;
  className?: string;
  children?: React.ReactNode;
  /** Minimum height for the drop zone */
  minHeight?: string;
  /** Whether to show drop indicator when empty */
  showIndicator?: boolean;
  /** Custom drop indicator content */
  indicatorContent?: React.ReactNode;
  /** Whether the drop zone is disabled */
  disabled?: boolean;
  /** Validation function for drop operations */
  canAcceptDrop?: (item: BlockTypeDragItem | BlockInstanceDragItem) => boolean;
  /** Callback for drop validation errors */
  onDropError?: (error: string) => void;
  /** ARIA label for accessibility */
  ariaLabel?: string;
}

export const DropZone: React.FC<DropZoneProps> = React.memo(({
  onDrop,
  onBlockMove,
  className,
  children,
  minHeight = 'auto',
  showIndicator = true,
  indicatorContent,
  disabled = false,
  canAcceptDrop,
  onDropError,
  ariaLabel = 'Drop zone for blocks',
}) => {
  const dropRef = useRef<HTMLDivElement>(null);

  // Memoized drop validation
  const validateDrop = useCallback((item: BlockTypeDragItem | BlockInstanceDragItem): boolean => {
    if (disabled) {
      onDropError?.('Drop zone is disabled');
      return false;
    }

    if (canAcceptDrop && !canAcceptDrop(item)) {
      onDropError?.('This item cannot be dropped here');
      return false;
    }

    return true;
  }, [disabled, canAcceptDrop, onDropError]);

  // Enhanced drop handler with validation
  const handleDrop = useCallback((item: BlockTypeDragItem | BlockInstanceDragItem) => {
    if (!validateDrop(item)) {
      return;
    }

    try {
      if (item.type === ItemTypes.BLOCK_TYPE) {
        onDrop(item.blockType);
      } else if (item.type === ItemTypes.BLOCK_INSTANCE) {
        onBlockMove?.(item.blockId, item.columnId, item.index);
      }
    } catch (error) {
      onDropError?.(error instanceof Error ? error.message : 'Drop operation failed');
    }
  }, [onDrop, onBlockMove, validateDrop, onDropError]);

  const [{ isOver, canDrop, draggedItem }, drop] = useDrop({
    accept: [ItemTypes.BLOCK_TYPE, ItemTypes.BLOCK_INSTANCE],
    drop: handleDrop,
    canDrop: (item: unknown) => validateDrop(item as BlockTypeDragItem | BlockInstanceDragItem),
    collect: (monitor: DropTargetMonitor) => ({
      isOver: monitor.isOver(),
      canDrop: monitor.canDrop(),
      draggedItem: monitor.getItem() as BlockTypeDragItem | BlockInstanceDragItem | null,
    }),
  });

  // Connect the drop ref
  drop(dropRef);

  const isActive = isOver && canDrop;
  const isInvalid = isOver && !canDrop;

  // Memoized class names for performance
  const dropZoneClasses = useMemo(() => clsx(
    'relative transition-all duration-300 ease-in-out',
    'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50',
    {
      // Active drop state - enhanced with gradient and glow
      'bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 border-2 border-blue-400 border-dashed rounded-xl shadow-2xl transform scale-[1.02] ring-4 ring-blue-200 ring-opacity-50': isActive,
      // Hover state when can drop - subtle indication
      'border-2 border-gray-300 border-dashed rounded-lg bg-gradient-to-br from-gray-50 to-gray-100 shadow-sm': canDrop && !isOver,
      // Invalid drop state - clear error indication
      'bg-gradient-to-br from-red-50 to-pink-50 border-2 border-red-400 border-dashed rounded-lg shadow-lg ring-2 ring-red-200 ring-opacity-50': isInvalid,
      // Disabled state
      'opacity-50 cursor-not-allowed grayscale': disabled,
      // Default state - minimal hover effect
      'hover:bg-gradient-to-br hover:from-gray-50 hover:to-gray-100 hover:border-gray-200 hover:border-dashed hover:rounded-lg hover:shadow-sm': !disabled && !canDrop && !isOver,
    },
    className
  ), [isActive, canDrop, isOver, isInvalid, disabled, className]);

  // Memoized indicator content
  const dropIndicator = useMemo(() => {
    if (!showIndicator) return null;

    if (indicatorContent) {
      return indicatorContent;
    }

    if (isActive) {
      const isDraggedBlock = draggedItem?.type === ItemTypes.BLOCK_TYPE;
      const isDraggedInstance = draggedItem?.type === ItemTypes.BLOCK_INSTANCE;

      return (
        <div className="flex items-center justify-center p-8">
          <div className="text-center">
            <div className="relative mb-4">
              {/* Animated background rings */}
              <div className="absolute inset-0 animate-ping">
                <div className="w-16 h-16 bg-blue-400 rounded-full opacity-20"></div>
              </div>
              <div className="absolute inset-2 animate-pulse">
                <div className="w-12 h-12 bg-blue-500 rounded-full opacity-30"></div>
              </div>

              {/* Main icon */}
              <div className="relative z-10 w-16 h-16 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-full flex items-center justify-center shadow-lg">
                <svg
                  className="w-8 h-8 text-white animate-bounce"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2.5}
                    d="M12 4v16m8-8H4"
                  />
                </svg>
              </div>

              {/* Floating particles */}
              <div className="absolute -top-2 -right-2 w-3 h-3 bg-yellow-400 rounded-full animate-bounce delay-100"></div>
              <div className="absolute -bottom-1 -left-2 w-2 h-2 bg-green-400 rounded-full animate-bounce delay-200"></div>
              <div className="absolute top-1/2 -right-3 w-2 h-2 bg-purple-400 rounded-full animate-bounce delay-300"></div>
            </div>

            <div className="space-y-2">
              <p className="text-lg text-blue-700 font-bold tracking-wide">
                {isDraggedBlock ? '✨ Drop to add block' : isDraggedInstance ? '🔄 Drop to move block' : '🎯 Drop here'}
              </p>
              {(isDraggedBlock || isDraggedInstance) && (
                <div className="inline-flex items-center space-x-2 px-3 py-1 bg-blue-100 rounded-full">
                  <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                  <p className="text-sm text-blue-700 font-medium">
                    {isDraggedBlock && draggedItem ? `Adding ${draggedItem.blockType}` :
                     isDraggedInstance && draggedItem ? 'Moving block' : ''}
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      );
    }

    if (isInvalid) {
      return (
        <div className="flex items-center justify-center p-6">
          <div className="text-center">
            <div className="relative mb-3">
              {/* Error pulse effect */}
              <div className="absolute inset-0 animate-ping">
                <div className="w-12 h-12 bg-red-400 rounded-full opacity-30"></div>
              </div>

              {/* Error icon */}
              <div className="relative z-10 w-12 h-12 bg-gradient-to-br from-red-500 to-red-600 rounded-full flex items-center justify-center shadow-lg">
                <svg
                  className="w-6 h-6 text-white animate-pulse"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2.5}
                    d="M6 18L18 6M6 6l12 12"
                  />
                </svg>
              </div>
            </div>

            <p className="text-base text-red-700 font-semibold">❌ Cannot drop here</p>
            <p className="text-xs text-red-600 opacity-75 mt-1">This location doesn't accept this item</p>
          </div>
        </div>
      );
    }

    return null;
  }, [showIndicator, indicatorContent, isActive, isInvalid, draggedItem]);

  return (
    <div
      ref={dropRef}
      className={dropZoneClasses}
      style={{ minHeight }}
      role="region"
      aria-label={ariaLabel}
      data-testid="drop-zone"
      tabIndex={disabled ? -1 : 0}
    >
      {(isActive || isInvalid) && !children && dropIndicator}
      {children}
    </div>
  );
});

DropZone.displayName = 'DropZone';
