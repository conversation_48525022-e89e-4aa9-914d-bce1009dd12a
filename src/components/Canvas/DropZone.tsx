import React, { useCallback, useMemo, useRef } from 'react';
import { useDrop, DropTargetMonitor } from 'react-dnd';
import { clsx } from 'clsx';
import { ItemTypes, BlockTypeDragItem, BlockInstanceDragItem } from '../../hooks/useDragDrop';

export interface DropZoneProps {
  onDrop: (blockType: string) => void;
  onBlockMove?: (blockId: string, sourceColumnId: string, sourceIndex: number) => void;
  className?: string;
  children?: React.ReactNode;
  /** Minimum height for the drop zone */
  minHeight?: string;
  /** Whether to show drop indicator when empty */
  showIndicator?: boolean;
  /** Custom drop indicator content */
  indicatorContent?: React.ReactNode;
  /** Whether the drop zone is disabled */
  disabled?: boolean;
  /** Validation function for drop operations */
  canAcceptDrop?: (item: BlockTypeDragItem | BlockInstanceDragItem) => boolean;
  /** Callback for drop validation errors */
  onDropError?: (error: string) => void;
  /** ARIA label for accessibility */
  ariaLabel?: string;
}

export const DropZone: React.FC<DropZoneProps> = React.memo(({
  onDrop,
  onBlockMove,
  className,
  children,
  minHeight = 'auto',
  showIndicator = true,
  indicatorContent,
  disabled = false,
  canAcceptDrop,
  onDropError,
  ariaLabel = 'Drop zone for blocks',
}) => {
  const dropRef = useRef<HTMLDivElement>(null);

  // Memoized drop validation
  const validateDrop = useCallback((item: BlockTypeDragItem | BlockInstanceDragItem): boolean => {
    if (disabled) {
      onDropError?.('Drop zone is disabled');
      return false;
    }

    if (canAcceptDrop && !canAcceptDrop(item)) {
      onDropError?.('This item cannot be dropped here');
      return false;
    }

    return true;
  }, [disabled, canAcceptDrop, onDropError]);

  // Enhanced drop handler with validation
  const handleDrop = useCallback((item: BlockTypeDragItem | BlockInstanceDragItem) => {
    if (!validateDrop(item)) {
      return;
    }

    try {
      if (item.type === ItemTypes.BLOCK_TYPE) {
        onDrop(item.blockType);
      } else if (item.type === ItemTypes.BLOCK_INSTANCE) {
        onBlockMove?.(item.blockId, item.columnId, item.index);
      }
    } catch (error) {
      onDropError?.(error instanceof Error ? error.message : 'Drop operation failed');
    }
  }, [onDrop, onBlockMove, validateDrop, onDropError]);

  const [{ isOver, canDrop, draggedItem }, drop] = useDrop({
    accept: [ItemTypes.BLOCK_TYPE, ItemTypes.BLOCK_INSTANCE],
    drop: handleDrop,
    canDrop: (item: unknown) => validateDrop(item as BlockTypeDragItem | BlockInstanceDragItem),
    collect: (monitor: DropTargetMonitor) => ({
      isOver: monitor.isOver(),
      canDrop: monitor.canDrop(),
      draggedItem: monitor.getItem() as BlockTypeDragItem | BlockInstanceDragItem | null,
    }),
  });

  // Connect the drop ref
  drop(dropRef);

  const isActive = isOver && canDrop;
  const isInvalid = isOver && !canDrop;

  // Memoized class names for performance
  const dropZoneClasses = useMemo(() => clsx(
    'relative transition-all duration-300 ease-in-out',
    'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-opacity-50',
    {
      // Active drop state
      'bg-gradient-to-br from-blue-50 to-indigo-50 border-2 border-blue-300 border-dashed rounded-lg shadow-lg transform scale-[1.02]': isActive,
      // Hover state when can drop
      'border-2 border-gray-300 border-dashed rounded-lg bg-gray-50/50': canDrop && !isOver,
      // Invalid drop state
      'bg-red-50 border-2 border-red-300 border-dashed rounded-lg': isInvalid,
      // Disabled state
      'opacity-50 cursor-not-allowed': disabled,
      // Default state
      'hover:bg-gray-50/30 hover:border-gray-200 hover:border-dashed hover:rounded-lg': !disabled && !canDrop && !isOver,
    },
    className
  ), [isActive, canDrop, isOver, isInvalid, disabled, className]);

  // Memoized indicator content
  const dropIndicator = useMemo(() => {
    if (!showIndicator) return null;

    if (indicatorContent) {
      return indicatorContent;
    }

    if (isActive) {
      const isDraggedBlock = draggedItem?.type === ItemTypes.BLOCK_TYPE;
      const isDraggedInstance = draggedItem?.type === ItemTypes.BLOCK_INSTANCE;

      return (
        <div className="flex items-center justify-center p-6">
          <div className="text-center">
            <div className="relative">
              <svg
                className="w-12 h-12 text-blue-500 mx-auto mb-3 animate-bounce"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 4v16m8-8H4"
                />
              </svg>
              <div className="absolute -top-1 -right-1 w-4 h-4 bg-blue-500 rounded-full animate-pulse" />
            </div>
            <p className="text-base text-blue-700 font-semibold mb-1">
              {isDraggedBlock ? 'Drop to add block' : isDraggedInstance ? 'Drop to move block' : 'Drop here'}
            </p>
            <p className="text-xs text-blue-600 opacity-75">
              {isDraggedBlock && draggedItem ? `Adding ${draggedItem.blockType}` :
               isDraggedInstance && draggedItem ? 'Moving block' : ''}
            </p>
          </div>
        </div>
      );
    }

    if (isInvalid) {
      return (
        <div className="flex items-center justify-center p-6">
          <div className="text-center">
            <svg
              className="w-10 h-10 text-red-500 mx-auto mb-2"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"
              />
            </svg>
            <p className="text-sm text-red-600 font-medium">Cannot drop here</p>
          </div>
        </div>
      );
    }

    return null;
  }, [showIndicator, indicatorContent, isActive, isInvalid, draggedItem]);

  return (
    <div
      ref={dropRef}
      className={dropZoneClasses}
      style={{ minHeight }}
      role="region"
      aria-label={ariaLabel}
      data-testid="drop-zone"
      tabIndex={disabled ? -1 : 0}
    >
      {(isActive || isInvalid) && !children && dropIndicator}
      {children}
    </div>
  );
});

DropZone.displayName = 'DropZone';
