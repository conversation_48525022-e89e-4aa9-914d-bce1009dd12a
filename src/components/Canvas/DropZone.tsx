import React from 'react';
import { useDrop } from 'react-dnd';
import { clsx } from 'clsx';
import { ItemTypes, BlockTypeDragItem, BlockInstanceDragItem } from '../../hooks/useDragDrop';

export interface DropZoneProps {
  onDrop: (blockType: string) => void;
  onBlockMove?: (blockId: string, sourceColumnId: string, sourceIndex: number) => void;
  className?: string;
  children?: React.ReactNode;
}

export const DropZone: React.FC<DropZoneProps> = ({
  onDrop,
  onBlockMove,
  className,
  children,
}) => {
  const [{ isOver, canDrop }, drop] = useDrop({
    accept: [ItemTypes.BLOCK_TYPE, ItemTypes.BLOCK_INSTANCE],
    drop: (item: BlockTypeDragItem | BlockInstanceDragItem) => {
      if (item.type === ItemTypes.BLOCK_TYPE) {
        onDrop(item.blockType);
      } else if (item.type === ItemTypes.BLOCK_INSTANCE) {
        onBlockMove?.(item.blockId, item.columnId, item.index);
      }
    },
    collect: (monitor) => ({
      isOver: monitor.isOver(),
      canDrop: monitor.canDrop(),
    }),
  });

  const isActive = isOver && canDrop;

  return (
    <div
      ref={drop as any}
      className={clsx(
        'transition-all duration-200',
        isActive && 'bg-blue-100 border-blue-300 border-2 border-dashed rounded-lg',
        canDrop && !isOver && 'border-gray-300 border border-dashed rounded-lg',
        className
      )}
    >
      {isActive && !children && (
        <div className="flex items-center justify-center p-4">
          <div className="text-center">
            <svg
              className="w-8 h-8 text-blue-500 mx-auto mb-2"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M12 4v16m8-8H4"
              />
            </svg>
            <p className="text-sm text-blue-600 font-medium">Drop to add block</p>
          </div>
        </div>
      )}
      {children}
    </div>
  );
};
