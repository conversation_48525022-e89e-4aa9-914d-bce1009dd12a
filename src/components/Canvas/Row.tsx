import React, { useState } from 'react';
import { clsx } from 'clsx';
import { Row as RowType } from '../../types';
import { Column } from './Column';

export interface RowProps {
  row: RowType;
  index: number;
  selectedBlockId?: string | null;
  onColumnAdd?: (rowId: string, size: number, index?: number) => void;
  onColumnRemove?: (columnId: string) => void;
  onBlockAdd?: (columnId: string, blockType: string, index?: number) => void;
  onBlockRemove?: (blockId: string) => void;
  onBlockUpdate?: (blockId: string, updates: any) => void;
  onBlockSelect?: (blockId: string | null) => void;
  onBlockMove?: (blockId: string, targetColumnId: string, targetIndex: number) => void;
  onRowRemove?: (rowId: string) => void;
}

export const Row: React.FC<RowProps> = ({
  row,
  index,
  selectedBlockId,
  onColumnAdd,
  onColumnRemove,
  onBlockAdd,
  onBlockRemove,
  onBlockUpdate,
  onBlockSelect,
  onBlockMove,
  onRowRemove,
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const [showColumnOptions, setShowColumnOptions] = useState(false);

  const handleAddColumn = (size: number) => {
    onColumnAdd?.(row.id, size);
    setShowColumnOptions(false);
  };

  const getTotalColumns = () => {
    return row.columns.reduce((total, col) => total + col.size, 0);
  };

  const canAddColumn = () => {
    return getTotalColumns() < 12;
  };

  const styles = {
    backgroundColor: row.styles?.backgroundColor,
    backgroundImage: row.styles?.backgroundImage,
    padding: row.styles?.padding || '1rem 0',
    margin: row.styles?.margin,
    minHeight: row.styles?.minHeight,
  };

  return (
    <div
      className={clsx(
        'relative group border border-transparent hover:border-blue-200 rounded-lg transition-all duration-200',
        isHovered && 'bg-blue-50/30'
      )}
      style={styles}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Row controls */}
      {isHovered && (
        <div className="absolute -top-8 left-0 flex items-center space-x-2 bg-white border border-gray-200 rounded-md shadow-lg px-2 py-1 z-10">
          <span className="text-xs text-gray-500">Row {index + 1}</span>
          
          {canAddColumn() && (
            <div className="relative">
              <button
                onClick={() => setShowColumnOptions(!showColumnOptions)}
                className="p-1 text-gray-600 hover:text-blue-600 hover:bg-blue-50 rounded"
                title="Add Column"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                </svg>
              </button>
              
              {showColumnOptions && (
                <div className="absolute top-full left-0 mt-1 bg-white border border-gray-200 rounded-md shadow-lg py-1 z-20">
                  <div className="px-3 py-1 text-xs text-gray-500 font-medium">Add Column</div>
                  {[1, 2, 3, 4, 6, 12].map(size => {
                    const remaining = 12 - getTotalColumns();
                    if (size > remaining) return null;
                    return (
                      <button
                        key={size}
                        onClick={() => handleAddColumn(size)}
                        className="block w-full px-3 py-1 text-left text-xs hover:bg-gray-50"
                      >
                        {size}/12 ({Math.round((size / 12) * 100)}%)
                      </button>
                    );
                  })}
                </div>
              )}
            </div>
          )}
          
          <button
            onClick={() => onRowRemove?.(row.id)}
            className="p-1 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded"
            title="Delete Row"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
          </button>
        </div>
      )}

      {/* Row content */}
      <div className="grid grid-cols-12 gap-4 px-4">
        {row.columns.length === 0 ? (
          <div className="col-span-12 flex flex-col items-center justify-center p-8 border-2 border-dashed border-gray-300 rounded-lg">
            <svg
              className="w-12 h-12 text-gray-400 mb-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1}
                d="M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 10V7m0 10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2h-2a2 2 0 00-2 2"
              />
            </svg>
            <p className="text-gray-500 text-sm">Add columns to this row</p>
          </div>
        ) : (
          row.columns.map((column) => (
            <Column
              key={column.id}
              column={column}
              selectedBlockId={selectedBlockId}
              onBlockAdd={onBlockAdd}
              onBlockRemove={onBlockRemove}
              onBlockUpdate={onBlockUpdate}
              onBlockSelect={onBlockSelect}
              onBlockMove={onBlockMove}
              onColumnRemove={onColumnRemove}
            />
          ))
        )}
      </div>
    </div>
  );
};
