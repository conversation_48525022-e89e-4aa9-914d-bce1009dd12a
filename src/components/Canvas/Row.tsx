import React, { useState } from 'react';
import { clsx } from 'clsx';
import { Row as RowType } from '../../types';
import { Column } from './Column';

export interface RowProps {
  row: RowType;
  index: number;
  selectedBlockId?: string | null;
  onColumnAdd?: (rowId: string, size: number, index?: number) => void;
  onColumnRemove?: (columnId: string) => void;
  onBlockAdd?: (columnId: string, blockType: string, index?: number) => void;
  onBlockRemove?: (blockId: string) => void;
  onBlockUpdate?: (blockId: string, updates: any) => void;
  onBlockSelect?: (blockId: string | null) => void;
  onBlockMove?: (blockId: string, targetColumnId: string, targetIndex: number) => void;
  onRowRemove?: (rowId: string) => void;
}

export const Row: React.FC<RowProps> = ({
  row,
  index,
  selectedBlockId,
  onColumnAdd,
  onColumnRemove,
  onBlockAdd,
  onBlockRemove,
  onBlockUpdate,
  onBlockSelect,
  onBlockMove,
  onRowRemove,
}) => {
  const [isHovered, setIsHovered] = useState(false);
  const [showColumnOptions, setShowColumnOptions] = useState(false);

  const handleAddColumn = (size: number) => {
    onColumnAdd?.(row.id, size);
    setShowColumnOptions(false);
  };

  const getTotalColumns = () => {
    return row.columns.reduce((total, col) => total + col.size, 0);
  };

  const canAddColumn = () => {
    return getTotalColumns() < 12;
  };

  const styles = {
    backgroundColor: row.styles?.backgroundColor,
    backgroundImage: row.styles?.backgroundImage,
    padding: row.styles?.padding || '1rem 0',
    margin: row.styles?.margin,
    minHeight: row.styles?.minHeight,
  };

  return (
    <div
      className={clsx(
        'relative group border-2 border-transparent rounded-xl transition-all duration-300 transform hover:scale-[1.01]',
        'hover:border-blue-200 hover:shadow-lg hover:bg-gradient-to-r hover:from-blue-50/30 hover:to-indigo-50/30',
        isHovered && 'bg-gradient-to-r from-blue-50/50 to-indigo-50/50 shadow-md'
      )}
      style={styles}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Enhanced Row controls */}
      {isHovered && (
        <div className="absolute -top-12 left-1/2 transform -translate-x-1/2 flex items-center space-x-2 bg-white border border-gray-200 rounded-xl shadow-xl px-4 py-2 z-10 animate-in fade-in slide-in-from-top-2 duration-200">
          <div className="flex items-center space-x-2">
            <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
            <span className="text-xs font-semibold text-gray-700">Row {index + 1}</span>
            <span className="text-xs text-gray-500">({getTotalColumns()}/12 cols)</span>
          </div>

          <div className="h-4 w-px bg-gray-200"></div>

          {canAddColumn() && (
            <div className="relative">
              <button
                onClick={() => setShowColumnOptions(!showColumnOptions)}
                className={clsx(
                  "p-1.5 rounded-lg transition-all duration-200 transform hover:scale-110",
                  "text-gray-600 hover:text-blue-600 hover:bg-blue-100 hover:shadow-md",
                  showColumnOptions && "bg-blue-100 text-blue-600"
                )}
                title="Add Column"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                </svg>
              </button>

              {showColumnOptions && (
                <div className="absolute top-full left-0 mt-2 bg-white border border-gray-200 rounded-xl shadow-2xl py-2 z-30 min-w-[160px] animate-in fade-in slide-in-from-top-2 duration-200">
                  <div className="px-4 py-2 text-xs text-gray-500 font-semibold border-b border-gray-100">
                    Add Column
                  </div>
                  <div className="py-1">
                    {[1, 2, 3, 4, 6, 12].map(size => {
                      const remaining = 12 - getTotalColumns();
                      if (size > remaining) return null;
                      const percentage = Math.round((size / 12) * 100);
                      return (
                        <button
                          key={size}
                          onClick={() => handleAddColumn(size)}
                          className="flex items-center justify-between w-full px-4 py-2 text-xs hover:bg-blue-50 hover:text-blue-700 transition-colors duration-200"
                        >
                          <span className="font-medium">{size}/12</span>
                          <span className="text-gray-500">({percentage}%)</span>
                        </button>
                      );
                    })}
                  </div>
                </div>
              )}
            </div>
          )}

          <button
            onClick={() => onRowRemove?.(row.id)}
            className="p-1.5 rounded-lg transition-all duration-200 transform hover:scale-110 text-gray-600 hover:text-red-600 hover:bg-red-100 hover:shadow-md"
            title="Delete Row"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
          </button>
        </div>
      )}

      {/* Enhanced Row content */}
      <div className="grid grid-cols-12 gap-4 px-6 py-2">
        {row.columns.length === 0 ? (
          <div className="col-span-12 flex flex-col items-center justify-center p-12 border-2 border-dashed border-gray-300 rounded-xl hover:border-blue-400 hover:bg-blue-50/30 transition-all duration-300 group cursor-pointer">
            <div className="relative mb-6">
              {/* Animated column icon */}
              <div className="w-16 h-16 bg-gradient-to-br from-gray-100 to-gray-200 rounded-xl flex items-center justify-center shadow-lg group-hover:from-blue-100 group-hover:to-indigo-200 transition-all duration-300">
                <svg
                  className="w-8 h-8 text-gray-400 group-hover:text-blue-500 transition-colors duration-300"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={1.5}
                    d="M9 17V7m0 10a2 2 0 01-2 2H5a2 2 0 01-2-2V7a2 2 0 012-2h2a2 2 0 012 2m0 10a2 2 0 002 2h2a2 2 0 002-2M9 7a2 2 0 012-2h2a2 2 0 012 2m0 10V7m0 10a2 2 0 002 2h2a2 2 0 002-2V7a2 2 0 00-2-2h-2a2 2 0 00-2 2"
                  />
                </svg>
              </div>

              {/* Floating indicators */}
              <div className="absolute -top-1 -right-1 w-4 h-4 bg-blue-500 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 animate-pulse"></div>
              <div className="absolute -bottom-1 -left-1 w-3 h-3 bg-green-400 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300 animate-bounce delay-100"></div>
            </div>

            <h3 className="text-lg font-semibold text-gray-700 mb-2 group-hover:text-blue-700 transition-colors duration-300">
              Add columns to this row
            </h3>
            <p className="text-gray-500 text-sm text-center max-w-xs leading-relaxed">
              Click the + button above to add columns and start building your layout
            </p>

            {/* Quick action hints */}
            <div className="mt-6 flex items-center space-x-4 text-xs text-gray-400 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <div className="flex items-center space-x-1">
                <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                <span>Responsive</span>
              </div>
              <div className="flex items-center space-x-1">
                <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                <span>Flexible</span>
              </div>
              <div className="flex items-center space-x-1">
                <div className="w-2 h-2 bg-purple-400 rounded-full"></div>
                <span>Customizable</span>
              </div>
            </div>
          </div>
        ) : (
          row.columns.map((column, columnIndex) => (
            <Column
              key={column.id}
              column={column}
              selectedBlockId={selectedBlockId}
              onBlockAdd={onBlockAdd}
              onBlockRemove={onBlockRemove}
              onBlockUpdate={onBlockUpdate}
              onBlockSelect={onBlockSelect}
              onBlockMove={onBlockMove}
              onColumnRemove={onColumnRemove}
              className={clsx(
                "animate-in fade-in slide-in-from-bottom-4 duration-300",
                `delay-${columnIndex * 100}`
              )}
            />
          ))
        )}
      </div>
    </div>
  );
};
