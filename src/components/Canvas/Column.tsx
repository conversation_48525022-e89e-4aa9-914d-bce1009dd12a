import React, { useState } from 'react';
import { clsx } from 'clsx';
import { Column as ColumnType, Block } from '../../types';
import { blockRegistry } from '../Blocks/index';
import { DropZone } from './DropZone';
import { useBlockInstanceDrag } from '../../hooks/useDragDrop';

interface DraggableBlockProps {
  block: Block;
  index: number;
  columnId: string;
  selectedBlockId?: string | null;
  onBlockUpdate?: (blockId: string, updates: any) => void;
  onBlockSelect?: (blockId: string | null) => void;
  onBlockRemove?: (blockId: string) => void;
  onBlockDrop: (blockType: string, index: number) => void;
  onBlockMove: (blockId: string, sourceColumnId: string, sourceIndex: number, targetIndex: number) => void;
}

const DraggableBlock: React.FC<DraggableBlockProps> = ({
  block,
  index,
  columnId,
  selectedBlockId,
  onBlockUpdate,
  onBlockSelect,
  onBlockRemove,
  onBlockDrop,
  onBlockMove,
}) => {
  const BlockComponent = blockRegistry.getBlockComponent(block.type);
  const { isDragging, drag } = useBlockInstanceDrag(block.id, columnId, index);

  if (!BlockComponent) {
    return (
      <div className="p-4 bg-red-100 border border-red-300 rounded">
        <p className="text-red-600 text-sm">Unknown block type: {block.type}</p>
      </div>
    );
  }

  return (
    <div 
      ref={drag as any}
      className={clsx(
        "relative group",
        isDragging && "opacity-50"
      )}
      style={{ cursor: isDragging ? 'grabbing' : 'grab' }}
    >
      <BlockComponent
        block={block}
        isSelected={selectedBlockId === block.id}
        onUpdate={(updates: any) => onBlockUpdate?.(block.id, updates)}
        onSelect={() => onBlockSelect?.(block.id)}
      />
      
      {/* Block controls */}
      {selectedBlockId === block.id && (
        <div className="absolute -top-8 right-0 flex items-center space-x-1 bg-white border border-gray-200 rounded-md shadow-lg px-2 py-1 z-10">
          <button
            onClick={() => onBlockRemove?.(block.id)}
            className="p-1 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded"
            title="Delete Block"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
          </button>
        </div>
      )}
      
      <DropZone
        onDrop={(blockType) => onBlockDrop(blockType, index + 1)}
        onBlockMove={(blockId, sourceColumnId, sourceIndex) => onBlockMove(blockId, sourceColumnId, sourceIndex, index + 1)}
        className="h-2 opacity-0 group-hover:opacity-100 transition-opacity"
      />
    </div>
  );
};

export interface ColumnProps {
  column: ColumnType;
  selectedBlockId?: string | null;
  onBlockAdd?: (columnId: string, blockType: string, index?: number) => void;
  onBlockRemove?: (blockId: string) => void;
  onBlockUpdate?: (blockId: string, updates: any) => void;
  onBlockSelect?: (blockId: string | null) => void;
  onBlockMove?: (blockId: string, targetColumnId: string, targetIndex: number) => void;
  onColumnRemove?: (columnId: string) => void;
}

export const Column: React.FC<ColumnProps> = ({
  column,
  selectedBlockId,
  onBlockAdd,
  onBlockRemove,
  onBlockUpdate,
  onBlockSelect,
  onBlockMove,
  onColumnRemove,
}) => {
  const [isHovered, setIsHovered] = useState(false);

  const handleBlockDrop = (blockType: string, index: number) => {
    onBlockAdd?.(column.id, blockType, index);
  };

  const handleBlockMove = (blockId: string, sourceColumnId: string, sourceIndex: number, targetIndex: number) => {
    onBlockMove?.(blockId, column.id, targetIndex);
  };

  const renderBlock = (block: Block, index: number) => {
    return (
      <DraggableBlock
        key={block.id}
        block={block}
        index={index}
        columnId={column.id}
        selectedBlockId={selectedBlockId}
        onBlockUpdate={onBlockUpdate}
        onBlockSelect={onBlockSelect}
        onBlockRemove={onBlockRemove}
        onBlockDrop={handleBlockDrop}
        onBlockMove={handleBlockMove}
      />
    );
  };

  const getColumnClasses = () => {
    return `col-span-${column.size}`;
  };

  const styles = {
    backgroundColor: column.styles?.backgroundColor,
    padding: column.styles?.padding || '0.5rem',
    margin: column.styles?.margin,
    border: column.styles?.border,
    borderRadius: column.styles?.borderRadius,
  };

  return (
    <div
      className={clsx(
        getColumnClasses(),
        'relative min-h-24 border border-dashed border-transparent hover:border-gray-300 rounded transition-all duration-200',
        isHovered && 'bg-gray-50/50'
      )}
      style={styles}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Column controls */}
      {isHovered && (
        <div className="absolute -top-8 left-0 flex items-center space-x-2 bg-white border border-gray-200 rounded-md shadow-lg px-2 py-1 z-10">
          <span className="text-xs text-gray-500">
            {column.size}/12 ({Math.round((column.size / 12) * 100)}%)
          </span>
          <button
            onClick={() => onColumnRemove?.(column.id)}
            className="p-1 text-gray-600 hover:text-red-600 hover:bg-red-50 rounded"
            title="Delete Column"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
          </button>
        </div>
      )}

      {/* Column content */}
      <div className="space-y-4">
        {column.blocks.length === 0 ? (
          <DropZone
            onDrop={(blockType) => handleBlockDrop(blockType, 0)}
            onBlockMove={(blockId, sourceColumnId, sourceIndex) => handleBlockMove(blockId, sourceColumnId, sourceIndex, 0)}
            className="min-h-24 flex items-center justify-center"
          >
            <div className="text-center p-4">
              <svg
                className="w-8 h-8 text-gray-400 mx-auto mb-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={1}
                  d="M12 4v16m8-8H4"
                />
              </svg>
              <p className="text-xs text-gray-500">Drop blocks here</p>
            </div>
          </DropZone>
        ) : (
          <>
            <DropZone
              onDrop={(blockType) => handleBlockDrop(blockType, 0)}
              onBlockMove={(blockId, sourceColumnId, sourceIndex) => handleBlockMove(blockId, sourceColumnId, sourceIndex, 0)}
              className="h-2 opacity-0 hover:opacity-100 transition-opacity"
            />
            {column.blocks.map((block, index) => renderBlock(block, index))}
          </>
        )}
      </div>
    </div>
  );
};
