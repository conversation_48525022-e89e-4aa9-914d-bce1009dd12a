import React from 'react';
import { clsx } from 'clsx';
import { ContentState, Block } from '../../types';
import { Row } from './Row';

export interface CanvasProps {
  content: ContentState;
  selectedBlockId?: string | null;
  onRowAdd?: (index?: number) => void;
  onRowRemove?: (rowId: string) => void;
  onColumnAdd?: (rowId: string, size: number, index?: number) => void;
  onColumnRemove?: (columnId: string) => void;
  onBlockAdd?: (columnId: string, blockType: string, index?: number) => void;
  onBlockRemove?: (blockId: string) => void;
  onBlockUpdate?: (blockId: string, updates: Partial<Block>) => void;
  onBlockSelect?: (blockId: string | null) => void;
  onBlockMove?: (blockId: string, targetColumnId: string, targetIndex: number) => void;
  className?: string;
  viewport?: 'mobile' | 'tablet' | 'desktop';
}

export const Canvas: React.FC<CanvasProps> = ({
  content,
  selectedBlockId,
  onRowAdd,
  onRowRemove,
  onColumnAdd,
  onColumnRemove,
  onBlockAdd,
  onBlockRemove,
  onBlockUpdate,
  onBlockSelect,
  onBlockMove,
  className,
  viewport = 'desktop',
}) => {
  const getViewportClasses = () => {
    switch (viewport) {
      case 'mobile':
        return 'max-w-sm mx-auto';
      case 'tablet':
        return 'max-w-2xl mx-auto';
      case 'desktop':
        return 'max-w-full';
      default:
        return 'max-w-full';
    }
  };

  const handleEmptyCanvasClick = () => {
    if (content.rows.length === 0) {
      onRowAdd?.(0);
    }
  };

  return (
    <div
      className={clsx(
        'flex-1 bg-white p-4 overflow-auto',
        className
      )}
    >
      <div className={clsx('min-h-full bg-white shadow-lg', getViewportClasses())}>
        {content.rows.length === 0 ? (
            <div
              onClick={handleEmptyCanvasClick}
              className="flex flex-col items-center justify-center min-h-96 p-8 border-2 border-dashed border-gray-300 rounded-lg cursor-pointer hover:border-gray-400 transition-colors"
            >
              <svg
                className="w-16 h-16 text-gray-400 mb-4"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={1}
                  d="M12 4v16m8-8H4"
                />
              </svg>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Start building your content
              </h3>
              <p className="text-gray-500 text-center max-w-sm">
                Click here to add your first row, or drag blocks from the sidebar to get started.
              </p>
            </div>
          ) : (
            <div className="space-y-4 p-4">
              {content.rows.map((row, index) => (
                <Row
                  key={row.id}
                  row={row}
                  index={index}
                  selectedBlockId={selectedBlockId}
                  onColumnAdd={onColumnAdd}
                  onColumnRemove={onColumnRemove}
                  onBlockAdd={onBlockAdd}
                  onBlockRemove={onBlockRemove}
                  onBlockUpdate={onBlockUpdate}
                  onBlockSelect={onBlockSelect}
                  onBlockMove={onBlockMove}
                  onRowRemove={onRowRemove}
                />
              ))}
              
              {/* Add row button */}
              <div className="flex justify-center pt-4">
                <button
                  onClick={() => onRowAdd?.(content.rows.length)}
                  className="flex items-center space-x-2 px-4 py-2 text-gray-600 border-2 border-dashed border-gray-300 rounded-lg hover:border-gray-400 hover:text-gray-800 transition-colors"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                  </svg>
                  <span>Add Row</span>
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
  );
};
