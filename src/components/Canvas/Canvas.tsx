import React from 'react';
import { clsx } from 'clsx';
import { ContentState, Block } from '../../types';
import { Row } from './Row';

export interface CanvasProps {
  content: ContentState;
  selectedBlockId?: string | null;
  onRowAdd?: (index?: number) => void;
  onRowRemove?: (rowId: string) => void;
  onColumnAdd?: (rowId: string, size: number, index?: number) => void;
  onColumnRemove?: (columnId: string) => void;
  onBlockAdd?: (columnId: string, blockType: string, index?: number) => void;
  onBlockRemove?: (blockId: string) => void;
  onBlockUpdate?: (blockId: string, updates: Partial<Block>) => void;
  onBlockSelect?: (blockId: string | null) => void;
  onBlockMove?: (blockId: string, targetColumnId: string, targetIndex: number) => void;
  className?: string;
  viewport?: 'mobile' | 'tablet' | 'desktop';
}

export const Canvas: React.FC<CanvasProps> = ({
  content,
  selectedBlockId,
  onRowAdd,
  onRowRemove,
  onColumnAdd,
  onColumnRemove,
  onBlockAdd,
  onBlockRemove,
  onBlockUpdate,
  onBlockSelect,
  onBlockMove,
  className,
  viewport = 'desktop',
}) => {
  const getViewportClasses = () => {
    switch (viewport) {
      case 'mobile':
        return 'max-w-sm mx-auto transition-all duration-500 ease-in-out';
      case 'tablet':
        return 'max-w-2xl mx-auto transition-all duration-500 ease-in-out';
      case 'desktop':
        return 'max-w-full transition-all duration-500 ease-in-out';
      default:
        return 'max-w-full transition-all duration-500 ease-in-out';
    }
  };

  const getViewportIndicator = () => {
    const indicators = {
      mobile: { icon: '📱', label: 'Mobile View', width: '375px' },
      tablet: { icon: '📱', label: 'Tablet View', width: '768px' },
      desktop: { icon: '🖥️', label: 'Desktop View', width: '100%' },
    };
    return indicators[viewport];
  };

  const handleEmptyCanvasClick = () => {
    if (content.rows.length === 0) {
      onRowAdd?.(0);
    }
  };

  return (
    <div
      className={clsx(
        'flex-1 bg-gradient-to-br from-gray-50 to-gray-100 p-6 overflow-auto',
        className
      )}
    >
      {/* Viewport Indicator */}
      {viewport !== 'desktop' && (
        <div className="flex items-center justify-center mb-4">
          <div className="flex items-center space-x-2 px-3 py-1 bg-white rounded-full shadow-sm border border-gray-200">
            <span className="text-sm">{getViewportIndicator().icon}</span>
            <span className="text-xs font-medium text-gray-600">{getViewportIndicator().label}</span>
            <span className="text-xs text-gray-400">({getViewportIndicator().width})</span>
          </div>
        </div>
      )}

      <div className={clsx(
        'min-h-full bg-white shadow-xl rounded-lg border border-gray-200 overflow-hidden',
        getViewportClasses()
      )}>
        {content.rows.length === 0 ? (
            <div
              onClick={handleEmptyCanvasClick}
              className="flex flex-col items-center justify-center min-h-96 p-12 border-2 border-dashed border-gray-300 rounded-lg cursor-pointer hover:border-blue-400 hover:bg-blue-50/30 transition-all duration-300 group"
            >
              <div className="relative">
                <svg
                  className="w-20 h-20 text-gray-400 mb-6 group-hover:text-blue-500 transition-colors duration-300"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={1.5}
                    d="M12 4v16m8-8H4"
                  />
                </svg>
                <div className="absolute -top-2 -right-2 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clipRule="evenodd" />
                  </svg>
                </div>
              </div>
              <h3 className="text-xl font-semibold text-gray-900 mb-3 group-hover:text-blue-700 transition-colors duration-300">
                Start building your content
              </h3>
              <p className="text-gray-500 text-center max-w-md leading-relaxed">
                Click here to add your first row, or drag blocks from the sidebar to get started building your page.
              </p>
              <div className="mt-6 flex items-center space-x-4 text-sm text-gray-400">
                <div className="flex items-center space-x-1">
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z" />
                  </svg>
                  <span>Drag & Drop</span>
                </div>
                <div className="flex items-center space-x-1">
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M3 5a2 2 0 012-2h10a2 2 0 012 2v8a2 2 0 01-2 2h-2.22l.123.489.804.804A1 1 0 0113 18H7a1 1 0 01-.707-1.707l.804-.804L7.22 15H5a2 2 0 01-2-2V5zm5.771 7H5V5h10v7H8.771z" clipRule="evenodd" />
                  </svg>
                  <span>Responsive</span>
                </div>
                <div className="flex items-center space-x-1">
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M12.316 3.051a1 1 0 01.633 1.265l-4 12a1 1 0 11-1.898-.632l4-12a1 1 0 011.265-.633zM5.707 6.293a1 1 0 010 1.414L3.414 10l2.293 2.293a1 1 0 11-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0zm8.586 0a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 11-1.414-1.414L16.586 10l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                  <span>Export Code</span>
                </div>
              </div>
            </div>
          ) : (
            <div className="space-y-4 p-4">
              {content.rows.map((row, index) => (
                <Row
                  key={row.id}
                  row={row}
                  index={index}
                  selectedBlockId={selectedBlockId}
                  onColumnAdd={onColumnAdd}
                  onColumnRemove={onColumnRemove}
                  onBlockAdd={onBlockAdd}
                  onBlockRemove={onBlockRemove}
                  onBlockUpdate={onBlockUpdate}
                  onBlockSelect={onBlockSelect}
                  onBlockMove={onBlockMove}
                  onRowRemove={onRowRemove}
                />
              ))}
              
              {/* Add row button */}
              <div className="flex justify-center pt-4">
                <button
                  onClick={() => onRowAdd?.(content.rows.length)}
                  className="flex items-center space-x-2 px-4 py-2 text-gray-600 border-2 border-dashed border-gray-300 rounded-lg hover:border-gray-400 hover:text-gray-800 transition-colors"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                  </svg>
                  <span>Add Row</span>
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
  );
};
