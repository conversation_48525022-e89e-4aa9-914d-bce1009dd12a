import React from 'react';
import { Block as BlockType } from '../../types';
import { blockComponents } from '../Blocks';

interface BlockProps {
  block: BlockType;
  isSelected: boolean;
  onSelect: (blockId: string) => void;
  onUpdate: (blockId: string, updates: Partial<BlockType>) => void;
  onRemove: (blockId: string) => void;
}

export const Block = React.memo(({
  block,
  isSelected,
  onSelect,
  onUpdate,
  onRemove
}: BlockProps) => {
  const BlockComponent = blockComponents[block.type];
  
  if (!BlockComponent) {
    return <div>Unknown block type: {block.type}</div>;
  }
  
  const handleSelect = () => {
    onSelect(block.id);
  };
  
  const handleUpdate = (updates: Partial<BlockType>) => {
    onUpdate(block.id, updates);
  };
  
  const handleRemove = () => {
    onRemove(block.id);
  };
  
  return (
    <div 
      className={`block-wrapper ${isSelected ? 'selected' : ''}`}
      onClick={handleSelect}
    >
      <BlockComponent
        block={block}
        isSelected={isSelected}
        onUpdate={handleUpdate}
        onRemove={handleRemove}
      />
    </div>
  );
}, (prevProps, nextProps) => {
  // Custom comparison function for more granular control
  return (
    prevProps.block.id === nextProps.block.id &&
    prevProps.isSelected === nextProps.isSelected &&
    JSON.stringify(prevProps.block.content) === JSON.stringify(nextProps.block.content) &&
    JSON.stringify(prevProps.block.styles) === JSON.stringify(nextProps.block.styles)
  );
});