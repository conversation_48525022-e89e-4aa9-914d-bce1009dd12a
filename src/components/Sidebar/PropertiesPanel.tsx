import React from 'react';
import { Block } from '../../types';
import { Button } from '../UI/Button';
import { Input } from '../UI/Input';
import { ColorPicker } from '../UI/ColorPicker';
import { Slider } from '../UI/Slider';

interface PropertiesPanelProps {
  block: Block;
  onUpdate: (blockId: string, updates: Partial<Block>) => void;
  onDelete: (blockId: string) => void;
  onDuplicate: (blockId: string) => void;
}

export const PropertiesPanel: React.FC<PropertiesPanelProps> = ({
  block,
  onUpdate,
  onDelete,
  onDuplicate,
}) => {
  const handleStyleUpdate = (property: string, value: any) => {
    onUpdate(block.id, {
      styles: {
        ...block.styles,
        [property]: value,
      },
    });
  };

  const handleContentUpdate = (value: any) => {
    onUpdate(block.id, {
      content: value,
    });
  };

  const handleAttributeUpdate = (property: string, value: any) => {
    onUpdate(block.id, {
      attributes: {
        ...block.attributes,
        [property]: value,
      },
    });
  };

  const renderBlockSpecificProps = () => {
    switch (block.type) {
      case 'text':
        return (
          <div className="space-y-3">
            <div>
              <label className="block text-xs font-medium text-gray-700 mb-1">
                Content
              </label>
              <textarea
                value={block.content?.text || ''}
                onChange={(e) => handleContentUpdate({ ...block.content, text: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm resize-none"
                rows={3}
                placeholder="Enter text content..."
              />
            </div>
            <div>
              <label className="block text-xs font-medium text-gray-700 mb-1">
                Tag
              </label>
              <select
                value={block.content?.tag || 'p'}
                onChange={(e) => handleContentUpdate({ ...block.content, tag: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
              >
                <option value="p">Paragraph</option>
                <option value="h1">Heading 1</option>
                <option value="h2">Heading 2</option>
                <option value="h3">Heading 3</option>
                <option value="h4">Heading 4</option>
                <option value="h5">Heading 5</option>
                <option value="h6">Heading 6</option>
              </select>
            </div>
          </div>
        );

      case 'image':
        return (
          <div className="space-y-3">
            <div>
              <label className="block text-xs font-medium text-gray-700 mb-1">
                Image URL
              </label>
              <Input
                value={block.content?.src || ''}
                onChange={(e) => handleContentUpdate({ ...block.content, src: e.target.value })}
                placeholder="https://example.com/image.jpg"
              />
            </div>
            <div>
              <label className="block text-xs font-medium text-gray-700 mb-1">
                Alt Text
              </label>
              <Input
                value={block.attributes?.alt || ''}
                onChange={(e) => handleAttributeUpdate('alt', e.target.value)}
                placeholder="Image description"
              />
            </div>
          </div>
        );

      case 'button':
        return (
          <div className="space-y-3">
            <div>
              <label className="block text-xs font-medium text-gray-700 mb-1">
                Button Text
              </label>
              <Input
                value={block.content?.text || ''}
                onChange={(e) => handleContentUpdate({ ...block.content, text: e.target.value })}
                placeholder="Button Text"
              />
            </div>
            <div>
              <label className="block text-xs font-medium text-gray-700 mb-1">
                Link URL
              </label>
              <Input
                value={block.attributes?.href || ''}
                onChange={(e) => handleAttributeUpdate('href', e.target.value)}
                placeholder="https://example.com"
              />
            </div>
            <div>
              <label className="block text-xs font-medium text-gray-700 mb-1">
                Variant
              </label>
              <select
                value={block.content?.variant || 'primary'}
                onChange={(e) => handleContentUpdate({ ...block.content, variant: e.target.value })}
                className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
              >
                <option value="primary">Primary</option>
                <option value="secondary">Secondary</option>
                <option value="outline">Outline</option>
                <option value="ghost">Ghost</option>
              </select>
            </div>
          </div>
        );

      case 'spacer':
        return (
          <div className="space-y-3">
            <div>
              <label className="block text-xs font-medium text-gray-700 mb-1">
                Height (px)
              </label>
              <Slider
                value={parseInt(block.styles?.height?.replace('px', '') || '20')}
                onChange={(value) => handleStyleUpdate('height', `${value}px`)}
                min={10}
                max={200}
                step={10}
              />
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="space-y-4">
      {/* Block Info */}
      <div className="pb-3 border-b border-gray-200">
        <h4 className="text-sm font-medium text-gray-900 capitalize">{block.type} Block</h4>
        <p className="text-xs text-gray-500 mt-1">ID: {block.id.slice(0, 8)}...</p>
      </div>

      {/* Block-specific Properties */}
      {renderBlockSpecificProps()}

      {/* Common Style Properties */}
      <div className="pt-3 border-t border-gray-200">
        <h5 className="text-xs font-medium text-gray-700 mb-3">Styling</h5>
        
        <div className="space-y-3">
          {/* Background Color */}
          <div>
            <label className="block text-xs font-medium text-gray-700 mb-1">
              Background Color
            </label>
            <ColorPicker
              value={block.styles?.backgroundColor || '#ffffff'}
              onChange={(color) => handleStyleUpdate('backgroundColor', color)}
            />
          </div>

          {/* Text Color */}
          {(block.type === 'text' || block.type === 'button') && (
            <div>
              <label className="block text-xs font-medium text-gray-700 mb-1">
                Text Color
              </label>
              <ColorPicker
                value={block.styles?.color || '#000000'}
                onChange={(color) => handleStyleUpdate('color', color)}
              />
            </div>
          )}

          {/* Padding */}
          <div>
            <label className="block text-xs font-medium text-gray-700 mb-1">
              Padding (px)
            </label>
            <Input
              type="number"
              value={block.styles?.padding?.replace('px', '') || ''}
              onChange={(e) => handleStyleUpdate('padding', `${e.target.value}px`)}
              placeholder="16"
            />
          </div>

          {/* Margin */}
          <div>
            <label className="block text-xs font-medium text-gray-700 mb-1">
              Margin (px)
            </label>
            <Input
              type="number"
              value={block.styles?.margin?.replace('px', '') || ''}
              onChange={(e) => handleStyleUpdate('margin', `${e.target.value}px`)}
              placeholder="8"
            />
          </div>

          {/* Border Radius */}
          <div>
            <label className="block text-xs font-medium text-gray-700 mb-1">
              Border Radius (px)
            </label>
            <Slider
              value={parseInt(block.styles?.borderRadius?.replace('px', '') || '0')}
              onChange={(value) => handleStyleUpdate('borderRadius', `${value}px`)}
              min={0}
              max={50}
              step={1}
            />
          </div>
        </div>
      </div>

      {/* Actions */}
      <div className="pt-3 border-t border-gray-200">
        <div className="flex space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => onDuplicate(block.id)}
            className="flex-1"
          >
            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
            </svg>
            Duplicate
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => onDelete(block.id)}
            className="flex-1 text-red-600 hover:text-red-700 hover:bg-red-50"
          >
            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
            Delete
          </Button>
        </div>
      </div>
    </div>
  );
};
