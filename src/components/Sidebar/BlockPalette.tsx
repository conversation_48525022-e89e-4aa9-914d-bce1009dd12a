import React, { useState } from 'react';
import { clsx } from 'clsx';
import { blockRegistry } from '../Blocks';
import { useBlockTypeDrag } from '../../hooks/useDragDrop';

export interface BlockPaletteProps {
  className?: string;
  enabledBlocks?: string[];
}

interface BlockPaletteItemProps {
  blockType: string;
  name: string;
  icon: React.ComponentType;
  category: string;
}

const BlockPaletteItem: React.FC<BlockPaletteItemProps> = ({
  blockType,
  name,
  icon: Icon,
  category,
}) => {
  const { isDragging, drag } = useBlockTypeDrag(blockType);

  return (
    <div
      ref={drag as any}
      className={clsx(
        'group relative flex items-center space-x-3 p-4 bg-white border border-gray-200 rounded-xl cursor-grab transition-all duration-300 transform hover:scale-[1.02]',
        'hover:shadow-lg hover:border-blue-300 hover:bg-gradient-to-r hover:from-blue-50 hover:to-indigo-50',
        isDragging && 'opacity-60 cursor-grabbing scale-110 shadow-2xl border-blue-500 bg-gradient-to-r from-blue-100 to-indigo-100 z-50'
      )}
    >
      {/* Drag indicator */}
      {isDragging && (
        <div className="absolute -top-2 -right-2 w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center shadow-lg animate-pulse">
          <svg className="w-3 h-3 text-white" fill="currentColor" viewBox="0 0 20 20">
            <path d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z" />
          </svg>
        </div>
      )}

      <div className="flex-shrink-0">
        <div className={clsx(
          "w-10 h-10 rounded-xl flex items-center justify-center transition-all duration-300 shadow-sm",
          "bg-gradient-to-br from-gray-100 to-gray-200 group-hover:from-blue-100 group-hover:to-indigo-200",
          isDragging && "from-blue-200 to-indigo-300 shadow-lg"
        )}>
          <div className={clsx(
            "transition-all duration-300",
            isDragging && "scale-110"
          )}>
            <Icon />
          </div>
        </div>
      </div>

      <div className="flex-1 min-w-0">
        <p className={clsx(
          "text-sm font-semibold transition-colors duration-300 truncate",
          "text-gray-900 group-hover:text-blue-700",
          isDragging && "text-blue-800"
        )}>
          {name}
        </p>
        <p className={clsx(
          "text-xs capitalize transition-colors duration-300 truncate",
          "text-gray-500 group-hover:text-blue-600",
          isDragging && "text-blue-600"
        )}>
          {category}
        </p>
      </div>

      {/* Drag handle */}
      <div className={clsx(
        "flex-shrink-0 transition-all duration-300",
        "opacity-0 group-hover:opacity-100 transform translate-x-2 group-hover:translate-x-0",
        isDragging && "opacity-100 scale-110"
      )}>
        <div className="flex flex-col space-y-0.5">
          <div className="w-1 h-1 bg-gray-400 rounded-full"></div>
          <div className="w-1 h-1 bg-gray-400 rounded-full"></div>
          <div className="w-1 h-1 bg-gray-400 rounded-full"></div>
          <div className="w-1 h-1 bg-gray-400 rounded-full"></div>
          <div className="w-1 h-1 bg-gray-400 rounded-full"></div>
          <div className="w-1 h-1 bg-gray-400 rounded-full"></div>
        </div>
      </div>

      {/* Hover glow effect */}
      <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-blue-400 to-indigo-500 opacity-0 group-hover:opacity-10 transition-opacity duration-300 pointer-events-none"></div>
    </div>
  );
};

export const BlockPalette: React.FC<BlockPaletteProps> = ({
  className,
  enabledBlocks,
}) => {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');

  const allBlockTypes = blockRegistry.getAllBlockTypes();
  const categories = ['all', ...blockRegistry.getCategories()];

  // Filter blocks based on enabled blocks, category, and search term
  const filteredBlocks = Object.entries(allBlockTypes).filter(([type, blockType]) => {
    // Check if block is enabled
    if (enabledBlocks && !enabledBlocks.includes(type)) {
      return false;
    }

    // Check category filter
    if (selectedCategory !== 'all' && blockType.category !== selectedCategory) {
      return false;
    }

    // Check search term
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      return (
        blockType.name.toLowerCase().includes(searchLower) ||
        blockType.category.toLowerCase().includes(searchLower) ||
        type.toLowerCase().includes(searchLower)
      );
    }

    return true;
  });

  return (
    <div className={clsx('h-full flex flex-col', className)}>
      {/* Enhanced Search */}
      <div className="mb-6">
        <div className="relative group">
          <div className="absolute left-3 top-1/2 transform -translate-y-1/2 transition-colors duration-200">
            <svg className={clsx(
              "w-4 h-4 transition-colors duration-200",
              searchTerm ? "text-blue-500" : "text-gray-400 group-focus-within:text-blue-500"
            )} fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
          {searchTerm && (
            <button
              onClick={() => setSearchTerm('')}
              className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors duration-200"
            >
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          )}
          <input
            type="text"
            placeholder="Search components..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className={clsx(
              "w-full pl-10 py-3 text-sm border rounded-xl transition-all duration-200 bg-white shadow-sm",
              searchTerm ? "pr-10" : "pr-4",
              "border-gray-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus:shadow-md"
            )}
          />
        </div>

        {/* Search results count */}
        {searchTerm && (
          <div className="mt-2 text-xs text-gray-500">
            {filteredBlocks.length} component{filteredBlocks.length !== 1 ? 's' : ''} found
          </div>
        )}
      </div>

      {/* Enhanced Category filter */}
      <div className="mb-6">
        <div className="flex flex-wrap gap-2">
          {categories.map((category, index) => (
            <button
              key={category}
              onClick={() => setSelectedCategory(category)}
              className={clsx(
                'px-4 py-2 text-xs rounded-full transition-all duration-300 font-medium border transform hover:scale-105',
                selectedCategory === category
                  ? 'bg-gradient-to-r from-blue-500 to-indigo-600 text-white shadow-lg border-blue-500 ring-2 ring-blue-200'
                  : 'bg-white text-gray-700 hover:bg-gray-50 border-gray-200 hover:border-gray-300 shadow-sm hover:shadow-md'
              )}
              style={{
                animationDelay: `${index * 50}ms`
              }}
            >
              <span className="flex items-center space-x-1">
                {category === 'all' && <span>🎯</span>}
                {category === 'Content' && <span>📝</span>}
                {category === 'Layout' && <span>📐</span>}
                {category === 'Media' && <span>🖼️</span>}
                {category === 'Form' && <span>📋</span>}
                <span>{category === 'all' ? 'All' : category}</span>
              </span>
            </button>
          ))}
        </div>
      </div>

      {/* Block list */}
      <div className="flex-1 overflow-y-auto">
        <div className="space-y-2">
          {filteredBlocks.map(([type, blockType]) => (
            <BlockPaletteItem
              key={type}
              blockType={type}
              name={blockType.name}
              icon={blockType.icon}
              category={blockType.category}
            />
          ))}
        </div>

        {filteredBlocks.length === 0 && (
          <div className="text-center py-16">
            <div className="relative mb-6">
              {/* Animated search icon */}
              <div className="w-16 h-16 mx-auto bg-gradient-to-br from-gray-100 to-gray-200 rounded-full flex items-center justify-center shadow-lg">
                <svg
                  className="w-8 h-8 text-gray-400 animate-pulse"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={1.5}
                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                  />
                </svg>
              </div>

              {/* Floating dots */}
              <div className="absolute top-2 right-4 w-2 h-2 bg-blue-400 rounded-full animate-bounce delay-100"></div>
              <div className="absolute bottom-2 left-4 w-1.5 h-1.5 bg-purple-400 rounded-full animate-bounce delay-200"></div>
              <div className="absolute top-1/2 right-2 w-1 h-1 bg-green-400 rounded-full animate-bounce delay-300"></div>
            </div>

            <h3 className="text-lg font-semibold text-gray-700 mb-2">
              {searchTerm ? '🔍 No matches found' : '📦 No components available'}
            </h3>
            <p className="text-sm text-gray-500 mb-4 max-w-xs mx-auto leading-relaxed">
              {searchTerm
                ? `We couldn't find any components matching "${searchTerm}". Try adjusting your search or browse by category.`
                : 'No components are available in this category. Try selecting a different category or check back later.'
              }
            </p>

            {searchTerm && (
              <button
                onClick={() => setSearchTerm('')}
                className="inline-flex items-center space-x-2 px-4 py-2 bg-blue-500 text-white text-sm font-medium rounded-lg hover:bg-blue-600 transition-colors duration-200 shadow-sm hover:shadow-md"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
                <span>Clear search</span>
              </button>
            )}
          </div>
        )}
      </div>

      {/* Tips */}
      {!searchTerm && selectedCategory === 'all' && (
        <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-start space-x-2">
            <svg className="w-4 h-4 text-blue-500 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <div>
              <p className="text-xs font-medium text-blue-800 mb-1">Pro Tip</p>
              <p className="text-xs text-blue-700">Drag components directly onto the canvas to add them to your design.</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
