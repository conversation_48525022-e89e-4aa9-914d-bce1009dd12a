import React, { useState } from 'react';
import { clsx } from 'clsx';
import { blockRegistry } from '../Blocks';
import { useBlockTypeDrag } from '../../hooks/useDragDrop';

export interface BlockPaletteProps {
  className?: string;
  enabledBlocks?: string[];
}

interface BlockPaletteItemProps {
  blockType: string;
  name: string;
  icon: React.ComponentType;
  category: string;
}

const BlockPaletteItem: React.FC<BlockPaletteItemProps> = ({
  blockType,
  name,
  icon: Icon,
  category,
}) => {
  const { isDragging, drag } = useBlockTypeDrag(blockType);

  return (
    <div
      ref={drag as any}
      className={clsx(
        'group flex items-center space-x-3 p-3 bg-white border border-gray-200 rounded-lg cursor-grab hover:shadow-md hover:border-blue-300 hover:bg-blue-50 transition-all duration-200',
        isDragging && 'opacity-50 cursor-grabbing scale-105 shadow-lg border-blue-400'
      )}
    >
      <div className="flex-shrink-0 p-2 bg-gradient-to-br from-gray-100 to-gray-200 group-hover:from-blue-100 group-hover:to-blue-200 rounded-md transition-colors">
        <Icon />
      </div>
      <div className="flex-1 min-w-0">
        <p className="text-sm font-medium text-gray-900 truncate group-hover:text-blue-900">{name}</p>
        <p className="text-xs text-gray-500 truncate group-hover:text-blue-600">{category}</p>
      </div>
      <div className="opacity-0 group-hover:opacity-100 transition-opacity">
        <svg className="w-4 h-4 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
        </svg>
      </div>
    </div>
  );
};

export const BlockPalette: React.FC<BlockPaletteProps> = ({
  className,
  enabledBlocks,
}) => {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState('');

  const allBlockTypes = blockRegistry.getAllBlockTypes();
  const categories = ['all', ...blockRegistry.getCategories()];

  // Filter blocks based on enabled blocks, category, and search term
  const filteredBlocks = Object.entries(allBlockTypes).filter(([type, blockType]) => {
    // Check if block is enabled
    if (enabledBlocks && !enabledBlocks.includes(type)) {
      return false;
    }

    // Check category filter
    if (selectedCategory !== 'all' && blockType.category !== selectedCategory) {
      return false;
    }

    // Check search term
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      return (
        blockType.name.toLowerCase().includes(searchLower) ||
        blockType.category.toLowerCase().includes(searchLower) ||
        type.toLowerCase().includes(searchLower)
      );
    }

    return true;
  });

  return (
    <div className={clsx('h-full flex flex-col', className)}>
      {/* Search */}
      <div className="mb-4">
        <div className="relative">
          <svg className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
          </svg>
          <input
            type="text"
            placeholder="Search components..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2.5 text-sm border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white"
          />
        </div>
      </div>

      {/* Category filter */}
      <div className="mb-4">
        <div className="flex flex-wrap gap-1">
          {categories.map((category) => (
            <button
              key={category}
              onClick={() => setSelectedCategory(category)}
              className={clsx(
                'px-3 py-1.5 text-xs rounded-full transition-all duration-200 font-medium',
                selectedCategory === category
                  ? 'bg-blue-500 text-white shadow-sm'
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              )}
            >
              {category === 'all' ? 'All' : category}
            </button>
          ))}
        </div>
      </div>

      {/* Block list */}
      <div className="flex-1 overflow-y-auto">
        <div className="space-y-2">
          {filteredBlocks.map(([type, blockType]) => (
            <BlockPaletteItem
              key={type}
              blockType={type}
              name={blockType.name}
              icon={blockType.icon}
              category={blockType.category}
            />
          ))}
        </div>

        {filteredBlocks.length === 0 && (
          <div className="text-center py-12">
            <svg
              className="w-12 h-12 text-gray-300 mx-auto mb-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1}
                d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
              />
            </svg>
            <p className="text-sm text-gray-500 mb-1">No components found</p>
            <p className="text-xs text-gray-400">
              {searchTerm ? 'Try a different search term' : 'No components available in this category'}
            </p>
          </div>
        )}
      </div>

      {/* Tips */}
      {!searchTerm && selectedCategory === 'all' && (
        <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-start space-x-2">
            <svg className="w-4 h-4 text-blue-500 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            <div>
              <p className="text-xs font-medium text-blue-800 mb-1">Pro Tip</p>
              <p className="text-xs text-blue-700">Drag components directly onto the canvas to add them to your design.</p>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
