import React from 'react';
import { clsx } from 'clsx';

export interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  variant?: 'spinner' | 'dots' | 'pulse' | 'bars' | 'ring';
  color?: 'blue' | 'green' | 'purple' | 'red' | 'yellow' | 'gray';
  className?: string;
  text?: string;
  overlay?: boolean;
}

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'md',
  variant = 'spinner',
  color = 'blue',
  className,
  text,
  overlay = false,
}) => {
  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
    xl: 'w-12 h-12',
  };

  const colorClasses = {
    blue: 'text-blue-500',
    green: 'text-green-500',
    purple: 'text-purple-500',
    red: 'text-red-500',
    yellow: 'text-yellow-500',
    gray: 'text-gray-500',
  };

  const renderSpinner = () => {
    switch (variant) {
      case 'spinner':
        return (
          <svg
            className={clsx(sizeClasses[size], colorClasses[color], 'animate-spin')}
            fill="none"
            viewBox="0 0 24 24"
          >
            <circle
              className="opacity-25"
              cx="12"
              cy="12"
              r="10"
              stroke="currentColor"
              strokeWidth="4"
            />
            <path
              className="opacity-75"
              fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
            />
          </svg>
        );

      case 'dots':
        return (
          <div className="flex space-x-1">
            {[0, 1, 2].map((i) => (
              <div
                key={i}
                className={clsx(
                  'rounded-full bg-current animate-pulse',
                  size === 'sm' && 'w-1 h-1',
                  size === 'md' && 'w-1.5 h-1.5',
                  size === 'lg' && 'w-2 h-2',
                  size === 'xl' && 'w-3 h-3',
                  colorClasses[color]
                )}
                style={{
                  animationDelay: `${i * 0.2}s`,
                  animationDuration: '1s',
                }}
              />
            ))}
          </div>
        );

      case 'pulse':
        return (
          <div
            className={clsx(
              'rounded-full bg-current animate-pulse',
              sizeClasses[size],
              colorClasses[color]
            )}
          />
        );

      case 'bars':
        return (
          <div className="flex items-end space-x-0.5">
            {[0, 1, 2, 3].map((i) => (
              <div
                key={i}
                className={clsx(
                  'bg-current animate-pulse',
                  size === 'sm' && 'w-0.5 h-2',
                  size === 'md' && 'w-1 h-3',
                  size === 'lg' && 'w-1 h-4',
                  size === 'xl' && 'w-1.5 h-6',
                  colorClasses[color]
                )}
                style={{
                  animationDelay: `${i * 0.15}s`,
                  animationDuration: '0.8s',
                }}
              />
            ))}
          </div>
        );

      case 'ring':
        return (
          <div className={clsx('relative', sizeClasses[size])}>
            <div
              className={clsx(
                'absolute inset-0 rounded-full border-2 border-current opacity-25',
                colorClasses[color]
              )}
            />
            <div
              className={clsx(
                'absolute inset-0 rounded-full border-2 border-transparent border-t-current animate-spin',
                colorClasses[color]
              )}
            />
          </div>
        );

      default:
        return null;
    }
  };

  const content = (
    <div className={clsx('flex flex-col items-center justify-center space-y-2', className)}>
      {renderSpinner()}
      {text && (
        <p className={clsx(
          'font-medium',
          size === 'sm' && 'text-xs',
          size === 'md' && 'text-sm',
          size === 'lg' && 'text-base',
          size === 'xl' && 'text-lg',
          colorClasses[color]
        )}>
          {text}
        </p>
      )}
    </div>
  );

  if (overlay) {
    return (
      <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50">
        <div className="bg-white rounded-xl p-8 shadow-2xl">
          {content}
        </div>
      </div>
    );
  }

  return content;
};

// Preset loading components for common use cases
export const PageLoader: React.FC<{ text?: string }> = ({ text = 'Loading...' }) => (
  <LoadingSpinner size="lg" variant="spinner" text={text} overlay />
);

export const ButtonLoader: React.FC = () => (
  <LoadingSpinner size="sm" variant="spinner" color="gray" />
);

export const InlineLoader: React.FC<{ text?: string }> = ({ text }) => (
  <LoadingSpinner size="md" variant="dots" text={text} className="py-4" />
);

export const CardLoader: React.FC = () => (
  <div className="animate-pulse">
    <div className="bg-gray-200 rounded-lg h-48 mb-4"></div>
    <div className="space-y-2">
      <div className="bg-gray-200 rounded h-4 w-3/4"></div>
      <div className="bg-gray-200 rounded h-4 w-1/2"></div>
    </div>
  </div>
);

export const SkeletonLoader: React.FC<{ lines?: number; className?: string }> = ({ 
  lines = 3, 
  className 
}) => (
  <div className={clsx('animate-pulse space-y-2', className)}>
    {Array.from({ length: lines }).map((_, i) => (
      <div
        key={i}
        className={clsx(
          'bg-gray-200 rounded h-4',
          i === lines - 1 ? 'w-2/3' : 'w-full'
        )}
      />
    ))}
  </div>
);
