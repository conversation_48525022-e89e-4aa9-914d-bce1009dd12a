import React, { useState, useRef } from 'react';
import { clsx } from 'clsx';

export interface SliderProps {
  value: number;
  onChange: (value: number) => void;
  min?: number;
  max?: number;
  step?: number;
  label?: string;
  unit?: string;
  className?: string;
  disabled?: boolean;
  showValue?: boolean;
  showTicks?: boolean;
  color?: 'blue' | 'green' | 'purple' | 'red' | 'yellow';
  size?: 'sm' | 'md' | 'lg';
}

export const Slider: React.FC<SliderProps> = ({
  value,
  onChange,
  min = 0,
  max = 100,
  step = 1,
  label,
  unit = '',
  className,
  disabled = false,
  showValue = true,
  showTicks = false,
  color = 'blue',
  size = 'md',
}) => {
  const [isDragging, setIsDragging] = useState(false);
  const sliderRef = useRef<HTMLInputElement>(null);

  const percentage = ((value - min) / (max - min)) * 100;

  const colorClasses = {
    blue: 'from-blue-500 to-blue-600',
    green: 'from-green-500 to-green-600',
    purple: 'from-purple-500 to-purple-600',
    red: 'from-red-500 to-red-600',
    yellow: 'from-yellow-500 to-yellow-600',
  };

  const sizeClasses = {
    sm: { track: 'h-1', thumb: 'w-4 h-4', text: 'text-xs' },
    md: { track: 'h-2', thumb: 'w-5 h-5', text: 'text-sm' },
    lg: { track: 'h-3', thumb: 'w-6 h-6', text: 'text-base' },
  };

  const handleMouseDown = () => setIsDragging(true);
  const handleMouseUp = () => setIsDragging(false);

  const generateTicks = () => {
    if (!showTicks) return null;

    const tickCount = Math.min(10, (max - min) / step);
    const ticks = [];

    for (let i = 0; i <= tickCount; i++) {
      const tickValue = min + (i * (max - min)) / tickCount;
      const tickPercentage = ((tickValue - min) / (max - min)) * 100;

      ticks.push(
        <div
          key={i}
          className="absolute w-0.5 h-2 bg-gray-400 transform -translate-x-0.5"
          style={{ left: `${tickPercentage}%`, top: '100%' }}
        />
      );
    }

    return ticks;
  };

  return (
    <div className={clsx('space-y-3', className)}>
      {/* Header */}
      {(label || showValue) && (
        <div className="flex justify-between items-center">
          {label && (
            <label className={clsx('block font-medium text-gray-700', sizeClasses[size].text)}>
              {label}
            </label>
          )}
          {showValue && (
            <div className={clsx(
              'px-2 py-1 bg-gray-100 rounded-md font-mono font-medium text-gray-700 transition-colors duration-200',
              sizeClasses[size].text,
              isDragging && 'bg-blue-100 text-blue-700'
            )}>
              {value}{unit}
            </div>
          )}
        </div>
      )}

      {/* Slider Container */}
      <div className="relative px-2">
        {/* Track */}
        <div className={clsx(
          'relative w-full bg-gray-200 rounded-full overflow-hidden',
          sizeClasses[size].track
        )}>
          {/* Progress */}
          <div
            className={clsx(
              'absolute left-0 top-0 h-full bg-gradient-to-r rounded-full transition-all duration-200',
              colorClasses[color]
            )}
            style={{ width: `${percentage}%` }}
          />
        </div>

        {/* Ticks */}
        {generateTicks()}

        {/* Input */}
        <input
          ref={sliderRef}
          type="range"
          min={min}
          max={max}
          step={step}
          value={value}
          onChange={(e) => onChange(Number(e.target.value))}
          onMouseDown={handleMouseDown}
          onMouseUp={handleMouseUp}
          onTouchStart={handleMouseDown}
          onTouchEnd={handleMouseUp}
          disabled={disabled}
          className={clsx(
            'absolute top-0 left-0 w-full h-full appearance-none bg-transparent cursor-pointer focus:outline-none',
            disabled && 'cursor-not-allowed opacity-50'
          )}
          style={{
            background: 'transparent',
          }}
        />

        {/* Custom Thumb */}
        <div
          className={clsx(
            'absolute top-1/2 transform -translate-y-1/2 -translate-x-1/2 bg-white border-2 rounded-full shadow-lg transition-all duration-200 pointer-events-none',
            sizeClasses[size].thumb,
            isDragging ? 'scale-125 shadow-xl' : 'hover:scale-110',
            disabled ? 'border-gray-300' : `border-${color}-500`
          )}
          style={{ left: `${percentage}%` }}
        >
          <div className={clsx(
            'w-full h-full rounded-full bg-gradient-to-br opacity-20',
            colorClasses[color]
          )} />
        </div>
      </div>

      {/* Min/Max Labels */}
      {showTicks && (
        <div className="flex justify-between text-xs text-gray-500 px-2">
          <span>{min}{unit}</span>
          <span>{max}{unit}</span>
        </div>
      )}
    </div>
  );
};
