import React, { useState } from 'react';
import { clsx } from 'clsx';

export interface ColorPickerProps {
  value?: string;
  onChange?: (color: string) => void;
  label?: string;
  className?: string;
}

export const ColorPicker: React.FC<ColorPickerProps> = ({
  value = '#000000',
  onChange,
  label,
  className,
}) => {
  const [isOpen, setIsOpen] = useState(false);

  const handleColorChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange?.(e.target.value);
  };

  const presetColors = [
    '#000000', '#ffffff', '#f3f4f6', '#e5e7eb', '#d1d5db',
    '#ef4444', '#f97316', '#eab308', '#22c55e', '#06b6d4',
    '#3b82f6', '#8b5cf6', '#ec4899', '#f59e0b', '#10b981',
  ];

  return (
    <div className={clsx('space-y-2', className)}>
      {label && (
        <label className="block text-sm font-medium text-gray-700">{label}</label>
      )}
      
      <div className="flex items-center space-x-2">
        <div
          className="w-8 h-8 rounded border-2 border-gray-300 cursor-pointer"
          style={{ backgroundColor: value }}
          onClick={() => setIsOpen(!isOpen)}
        />
        <input
          type="text"
          value={value}
          onChange={(e) => onChange?.(e.target.value)}
          className="flex-1 px-3 py-1 text-sm border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500"
          placeholder="#000000"
        />
        <input
          type="color"
          value={value}
          onChange={handleColorChange}
          className="w-8 h-8 border border-gray-300 rounded cursor-pointer"
        />
      </div>

      {isOpen && (
        <div className="grid grid-cols-5 gap-2 p-2 border border-gray-200 rounded">
          {presetColors.map((color) => (
            <div
              key={color}
              className="w-8 h-8 rounded cursor-pointer border border-gray-300 hover:scale-110 transition-transform"
              style={{ backgroundColor: color }}
              onClick={() => {
                onChange?.(color);
                setIsOpen(false);
              }}
            />
          ))}
        </div>
      )}
    </div>
  );
};
