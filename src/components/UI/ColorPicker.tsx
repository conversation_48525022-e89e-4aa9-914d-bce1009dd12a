import React, { useState, useRef, useEffect } from 'react';
import { clsx } from 'clsx';

export interface ColorPickerProps {
  value?: string;
  onChange?: (color: string) => void;
  label?: string;
  className?: string;
  disabled?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

export const ColorPicker: React.FC<ColorPickerProps> = ({
  value = '#000000',
  onChange,
  label,
  className,
  disabled = false,
  size = 'md',
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [inputValue, setInputValue] = useState(value);
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    setInputValue(value);
  }, [value]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen]);

  const handleColorChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newColor = e.target.value;
    setInputValue(newColor);
    onChange?.(newColor);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInputValue(newValue);

    // Validate hex color
    if (/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/.test(newValue)) {
      onChange?.(newValue);
    }
  };

  const handlePresetClick = (color: string) => {
    setInputValue(color);
    onChange?.(color);
    setIsOpen(false);
  };

  const sizeClasses = {
    sm: { swatch: 'w-6 h-6', input: 'text-xs px-2 py-1', colorInput: 'w-6 h-6' },
    md: { swatch: 'w-8 h-8', input: 'text-sm px-3 py-2', colorInput: 'w-8 h-8' },
    lg: { swatch: 'w-10 h-10', input: 'text-base px-4 py-2', colorInput: 'w-10 h-10' },
  };

  const presetColors = [
    // Grays
    '#000000', '#374151', '#6b7280', '#9ca3af', '#d1d5db', '#f3f4f6', '#ffffff',
    // Colors
    '#ef4444', '#f97316', '#f59e0b', '#eab308', '#84cc16', '#22c55e', '#10b981',
    '#06b6d4', '#0ea5e9', '#3b82f6', '#6366f1', '#8b5cf6', '#a855f7', '#d946ef',
    '#ec4899', '#f43f5e',
  ];

  return (
    <div className={clsx('relative space-y-2', className)} ref={dropdownRef}>
      {label && (
        <label className="block text-sm font-medium text-gray-700">{label}</label>
      )}

      <div className="flex items-center space-x-2">
        {/* Color Swatch */}
        <button
          type="button"
          className={clsx(
            'relative rounded-lg border-2 border-gray-300 shadow-sm transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',
            sizeClasses[size].swatch,
            disabled ? 'cursor-not-allowed opacity-50' : 'cursor-pointer hover:scale-105 hover:shadow-md',
            isOpen && 'ring-2 ring-blue-500'
          )}
          style={{ backgroundColor: inputValue }}
          onClick={() => !disabled && setIsOpen(!isOpen)}
          disabled={disabled}
          aria-label="Open color picker"
        >
          {/* Checkerboard pattern for transparency */}
          <div className="absolute inset-0 rounded-lg opacity-20"
               style={{
                 backgroundImage: `url("data:image/svg+xml,%3csvg width='100%25' height='100%25' xmlns='http://www.w3.org/2000/svg'%3e%3cdefs%3e%3cpattern id='checkerboard' patternUnits='userSpaceOnUse' width='8' height='8'%3e%3crect width='4' height='4' fill='%23000'/%3e%3crect x='4' y='4' width='4' height='4' fill='%23000'/%3e%3c/pattern%3e%3c/defs%3e%3crect width='100%25' height='100%25' fill='url(%23checkerboard)'/%3e%3c/svg%3e")`
               }}
          />
        </button>

        {/* Text Input */}
        <input
          type="text"
          value={inputValue}
          onChange={handleInputChange}
          disabled={disabled}
          className={clsx(
            'flex-1 border border-gray-300 rounded-md shadow-sm transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
            sizeClasses[size].input,
            disabled && 'bg-gray-100 cursor-not-allowed'
          )}
          placeholder="#000000"
          pattern="^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$"
        />

        {/* Native Color Input */}
        <input
          type="color"
          value={inputValue}
          onChange={handleColorChange}
          disabled={disabled}
          className={clsx(
            'border border-gray-300 rounded-md cursor-pointer transition-all duration-200 hover:scale-105',
            sizeClasses[size].colorInput,
            disabled && 'cursor-not-allowed opacity-50'
          )}
          aria-label="Color picker"
        />
      </div>

      {/* Preset Colors Dropdown */}
      {isOpen && (
        <div className="absolute top-full left-0 right-0 z-50 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg p-3 animate-in fade-in slide-in-from-top-2 duration-200">
          <div className="mb-2">
            <span className="text-xs font-medium text-gray-700">Preset Colors</span>
          </div>
          <div className="grid grid-cols-7 gap-2">
            {presetColors.map((color) => (
              <button
                key={color}
                type="button"
                className={clsx(
                  'w-8 h-8 rounded-md border-2 transition-all duration-200 hover:scale-110 hover:shadow-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',
                  inputValue === color ? 'border-blue-500 ring-2 ring-blue-500' : 'border-gray-300'
                )}
                style={{ backgroundColor: color }}
                onClick={() => handlePresetClick(color)}
                aria-label={`Select color ${color}`}
                title={color}
              />
            ))}
          </div>

          {/* Recent/Custom Colors Section */}
          <div className="mt-3 pt-3 border-t border-gray-200">
            <span className="text-xs font-medium text-gray-700">Current</span>
            <div className="mt-2 flex items-center justify-center">
              <div
                className="w-12 h-8 rounded-md border-2 border-gray-300"
                style={{ backgroundColor: inputValue }}
              />
              <span className="ml-2 text-xs text-gray-600 font-mono">{inputValue}</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
