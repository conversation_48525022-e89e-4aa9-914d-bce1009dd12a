/* TailwindCSS v4 imports */
@import "tailwindcss";

/* Content Builder specific styles */
.content-builder {
  @apply h-full bg-white;
}

.content-builder-canvas {
  @apply flex-1 bg-gray-50 p-4 overflow-auto;
}

.content-builder-sidebar {
  @apply w-80 border-r border-gray-200 flex flex-col bg-white;
}

.content-builder-toolbar {
  @apply h-16 border-b border-gray-200 bg-white px-4 flex items-center justify-between;
}

.content-builder-row {
  @apply relative group border border-transparent hover:border-blue-200 rounded-lg transition-all duration-200;
}

.content-builder-column {
  @apply relative min-h-24 border border-dashed border-transparent hover:border-gray-300 rounded transition-all duration-200;
}

.content-builder-block {
  @apply relative group cursor-pointer transition-all duration-200;
}

.content-builder-block--selected {
  @apply ring-2 ring-blue-500 ring-offset-2;
}

.content-builder-dropzone {
  @apply transition-all duration-200;
}

.content-builder-dropzone--active {
  @apply bg-blue-100 border-blue-300 border-2 border-dashed rounded-lg;
}

.content-builder-dropzone--can-drop {
  @apply border-gray-300 border border-dashed rounded-lg;
}

/* Block specific styles */
.text-block {
  @apply outline-none;
}

.text-block:hover {
  @apply bg-gray-50;
}

.text-block--editing {
  @apply bg-white;
}

.image-block {
  @apply max-w-full h-auto;
}

.button-block {
  @apply inline-flex items-center px-4 py-2 text-sm font-medium rounded-md transition-colors cursor-pointer;
}

.divider-block {
  @apply border-0;
}

.spacer-block {
  @apply bg-transparent;
}

/* Responsive utilities */
@media (max-width: 768px) {
  .content-builder-sidebar {
    @apply hidden;
  }
}

/* Dark theme support */
.dark .content-builder {
  @apply bg-gray-900 text-white;
}

.dark .content-builder-canvas {
  @apply bg-gray-800;
}

.dark .content-builder-sidebar {
  @apply bg-gray-900 border-gray-700;
}

.dark .content-builder-toolbar {
  @apply bg-gray-900 border-gray-700;
}
