export interface ThemeTokens {
  colors: {
    primary: string;
    secondary: string;
    background: {
      main: string;
      sidebar: string;
      canvas: string;
    };
    text: {
      primary: string;
      secondary: string;
      muted: string;
    };
    border: string;
    selection: string;
  };
  spacing: {
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
  };
  borderRadius: {
    sm: string;
    md: string;
    lg: string;
  };
  shadows: {
    sm: string;
    md: string;
    lg: string;
  };
}

export const lightTheme: ThemeTokens = {
  colors: {
    primary: '#3b82f6',
    secondary: '#10b981',
    background: {
      main: '#f9fafb',
      sidebar: '#ffffff',
      canvas: '#ffffff',
    },
    text: {
      primary: '#111827',
      secondary: '#4b5563',
      muted: '#9ca3af',
    },
    border: '#e5e7eb',
    selection: 'rgba(59, 130, 246, 0.2)',
  },
  spacing: {
    xs: '0.25rem',
    sm: '0.5rem',
    md: '1rem',
    lg: '1.5rem',
    xl: '2rem',
  },
  borderRadius: {
    sm: '0.25rem',
    md: '0.5rem',
    lg: '0.75rem',
  },
  shadows: {
    sm: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
    md: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
    lg: '0 10px 15px -3px rgba(0, 0, 0, 0.1)',
  },
};

export const darkTheme: ThemeTokens = {
  // Dark theme implementation
};