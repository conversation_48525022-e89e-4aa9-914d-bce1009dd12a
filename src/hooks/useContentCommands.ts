import { useCallback } from 'react';
import { ContentState, Block, Row, Column } from '../types';
import { nanoid } from 'nanoid';

interface Command {
  execute: () => ContentState;
  undo: () => ContentState;
}

export function useContentCommands(content: ContentState, setContent: (content: ContentState) => void) {
  const createAddRowCommand = useCallback((index?: number): Command => {
    const newRow: Row = {
      id: nanoid(),
      columns: [],
      styles: {},
      attributes: {},
    };
    
    return {
      execute: () => {
        const newContent = { ...content };
        if (typeof index === 'number') {
          newContent.rows.splice(index, 0, newRow);
        } else {
          newContent.rows.push(newRow);
        }
        return newContent;
      },
      undo: () => {
        const newContent = { ...content };
        if (typeof index === 'number') {
          newContent.rows.splice(index, 1);
        } else {
          newContent.rows.pop();
        }
        return newContent;
      }
    };
  }, [content]);
  
  // Implement other commands (removeRow, addColumn, etc.)
  
  const executeCommand = useCallback((command: Command) => {
    const newContent = command.execute();
    setContent(newContent);
    // Add to history stack
    return newContent;
  }, [setContent]);
  
  return {
    addRow: (index?: number) => executeCommand(createAddRowCommand(index)),
    // Other command executors
  };
}
```
</