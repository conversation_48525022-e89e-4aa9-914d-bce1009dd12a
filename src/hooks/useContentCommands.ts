import { useCallback } from 'react';
import { ContentState, Block, Row, Column } from '../types';
import { nanoid } from 'nanoid';

interface Command {
  execute: () => ContentState;
  undo: () => ContentState;
}

export function useContentCommands(content: ContentState, setContent: (content: ContentState) => void) {
  const createAddRowCommand = useCallback((index?: number): Command => {
    const newRow: Row = {
      id: nanoid(),
      columns: [],
      styles: {},
      attributes: {},
    };
    
    return {
      execute: () => {
        const newContent = { ...content };
        if (typeof index === 'number') {
          newContent.rows.splice(index, 0, newRow);
        } else {
          newContent.rows.push(newRow);
        }
        return newContent;
      },
      undo: () => {
        const newContent = { ...content };
        if (typeof index === 'number') {
          newContent.rows.splice(index, 1);
        } else {
          newContent.rows.pop();
        }
        return newContent;
      }
    };
  }, [content]);
  
  // Implement other commands (removeRow, addColumn, etc.)
  
  const executeCommand = useCallback((command: Command) => {
    const newContent = command.execute();
    setContent(newContent);
    // Add to history stack
    return newContent;
  }, [setContent]);
  
  const createRemoveRowCommand = useCallback((rowId: string): Command => {
    const rowIndex = content.rows.findIndex(r => r.id === rowId);
    const removedRow = content.rows[rowIndex];

    return {
      execute: () => {
        const newContent = { ...content };
        newContent.rows.splice(rowIndex, 1);
        return newContent;
      },
      undo: () => {
        const newContent = { ...content };
        newContent.rows.splice(rowIndex, 0, removedRow);
        return newContent;
      }
    };
  }, [content]);

  const createAddColumnCommand = useCallback((rowId: string, size: number, index?: number): Command => {
    const newColumn: Column = {
      id: nanoid(),
      size,
      blocks: [],
      styles: {},
      attributes: {},
    };

    return {
      execute: () => {
        const newContent = { ...content };
        const row = newContent.rows.find(r => r.id === rowId);
        if (row) {
          if (typeof index === 'number') {
            row.columns.splice(index, 0, newColumn);
          } else {
            row.columns.push(newColumn);
          }
        }
        return newContent;
      },
      undo: () => {
        const newContent = { ...content };
        const row = newContent.rows.find(r => r.id === rowId);
        if (row) {
          const columnIndex = row.columns.findIndex(c => c.id === newColumn.id);
          if (columnIndex !== -1) {
            row.columns.splice(columnIndex, 1);
          }
        }
        return newContent;
      }
    };
  }, [content]);

  const createAddBlockCommand = useCallback((columnId: string, blockType: string, index?: number): Command => {
    const newBlock: Block = {
      id: nanoid(),
      type: blockType,
      content: {},
      styles: {},
      attributes: {},
    };

    return {
      execute: () => {
        const newContent = { ...content };
        const column = newContent.rows
          .flatMap(row => row.columns)
          .find(col => col.id === columnId);

        if (column) {
          if (typeof index === 'number') {
            column.blocks.splice(index, 0, newBlock);
          } else {
            column.blocks.push(newBlock);
          }
        }
        return newContent;
      },
      undo: () => {
        const newContent = { ...content };
        const column = newContent.rows
          .flatMap(row => row.columns)
          .find(col => col.id === columnId);

        if (column) {
          const blockIndex = column.blocks.findIndex(b => b.id === newBlock.id);
          if (blockIndex !== -1) {
            column.blocks.splice(blockIndex, 1);
          }
        }
        return newContent;
      }
    };
  }, [content]);

  return {
    addRow: (index?: number) => executeCommand(createAddRowCommand(index)),
    removeRow: (rowId: string) => executeCommand(createRemoveRowCommand(rowId)),
    addColumn: (rowId: string, size: number, index?: number) => executeCommand(createAddColumnCommand(rowId, size, index)),
    addBlock: (columnId: string, blockType: string, index?: number) => executeCommand(createAddBlockCommand(columnId, blockType, index)),
    executeCommand,
  };
}