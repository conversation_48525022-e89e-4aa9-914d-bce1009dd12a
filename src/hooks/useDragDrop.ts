import { useDrag, useDrop, DragSourceMonitor, DropTargetMonitor } from 'react-dnd';
import { useCallback } from 'react';

// Drag and drop item types
export const ItemTypes = {
  BLOCK_TYPE: 'BLOCK_TYPE',
  BLOCK_INSTANCE: 'BLOCK_INSTANCE',
  COLUMN: 'COLUMN',
  ROW: 'ROW',
} as const;

// Drag item interfaces
export interface BlockTypeDragItem {
  type: typeof ItemTypes.BLOCK_TYPE;
  blockType: string;
}

export interface BlockInstanceDragItem {
  type: typeof ItemTypes.BLOCK_INSTANCE;
  blockId: string;
  columnId: string;
  index: number;
}

export interface ColumnDragItem {
  type: typeof ItemTypes.COLUMN;
  columnId: string;
  rowId: string;
  index: number;
}

export interface RowDragItem {
  type: typeof ItemTypes.ROW;
  rowId: string;
  index: number;
}

// Hook for dragging block types from palette
export const useBlockTypeDrag = (blockType: string) => {
  const [{ isDragging }, drag] = useDrag({
    type: ItemTypes.BLOCK_TYPE,
    item: (): BlockTypeDragItem => ({
      type: ItemTypes.BLOCK_TYPE,
      blockType,
    }),
    collect: (monitor: DragSourceMonitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  return { isDragging, drag };
};

// Hook for dragging block instances
export const useBlockInstanceDrag = (blockId: string, columnId: string, index: number) => {
  const [{ isDragging }, drag] = useDrag({
    type: ItemTypes.BLOCK_INSTANCE,
    item: (): BlockInstanceDragItem => ({
      type: ItemTypes.BLOCK_INSTANCE,
      blockId,
      columnId,
      index,
    }),
    collect: (monitor: DragSourceMonitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  return { isDragging, drag };
};

// Hook for dropping blocks into columns
export const useColumnDrop = (
  columnId: string,
  onBlockDrop: (item: BlockTypeDragItem | BlockInstanceDragItem, targetIndex: number) => void
) => {
  const [{ isOver, canDrop }, drop] = useDrop({
    accept: [ItemTypes.BLOCK_TYPE, ItemTypes.BLOCK_INSTANCE],
    drop: (item: BlockTypeDragItem | BlockInstanceDragItem, monitor: DropTargetMonitor) => {
      if (!monitor.didDrop()) {
        const targetIndex = 0; // This would be calculated based on drop position
        onBlockDrop(item, targetIndex);
      }
    },
    collect: (monitor: DropTargetMonitor) => ({
      isOver: monitor.isOver(),
      canDrop: monitor.canDrop(),
    }),
  });

  return { isOver, canDrop, drop };
};

// Hook for reordering blocks within a column
export const useBlockReorder = (
  columnId: string,
  onBlockMove: (dragIndex: number, hoverIndex: number) => void
) => {
  const moveBlock = useCallback(
    (dragIndex: number, hoverIndex: number) => {
      onBlockMove(dragIndex, hoverIndex);
    },
    [onBlockMove]
  );

  return { moveBlock };
};

// Hook for drag preview
export const useDragPreview = () => {
  // This could be enhanced with custom drag preview components
  return {};
};

// Hook for handling drag over zones with position calculation
export const useDragOverZone = (
  onDragOver: (position: { x: number; y: number }, item: any) => void
) => {
  const handleDragOver = useCallback(
    (event: React.DragEvent, item: any) => {
      const rect = (event.currentTarget as HTMLElement).getBoundingClientRect();
      const position = {
        x: event.clientX - rect.left,
        y: event.clientY - rect.top,
      };
      onDragOver(position, item);
    },
    [onDragOver]
  );

  return { handleDragOver };
};

