import { useCallback, useEffect, useRef, useState } from 'react';

// Hook for debouncing values
export const useDebounce = <T>(value: T, delay: number): T => {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

// Hook for throttling functions
export const useThrottle = <T extends (...args: any[]) => any>(
  func: T,
  delay: number
): T => {
  const lastRan = useRef<number>(0);
  const timeoutRef = useRef<NodeJS.Timeout>();

  return useCallback(
    ((...args: Parameters<T>) => {
      const now = Date.now();
      
      if (now - lastRan.current >= delay) {
        func(...args);
        lastRan.current = now;
      } else {
        clearTimeout(timeoutRef.current);
        timeoutRef.current = setTimeout(() => {
          func(...args);
          lastRan.current = Date.now();
        }, delay - (now - lastRan.current));
      }
    }) as T,
    [func, delay]
  );
};

// Hook for intersection observer (lazy loading)
export const useIntersectionObserver = (
  options: IntersectionObserverInit = {}
) => {
  const [isIntersecting, setIsIntersecting] = useState(false);
  const [hasIntersected, setHasIntersected] = useState(false);
  const targetRef = useRef<HTMLElement | null>(null);

  useEffect(() => {
    const target = targetRef.current;
    if (!target) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsIntersecting(entry.isIntersecting);
        if (entry.isIntersecting && !hasIntersected) {
          setHasIntersected(true);
        }
      },
      {
        threshold: 0.1,
        rootMargin: '50px',
        ...options,
      }
    );

    observer.observe(target);

    return () => {
      observer.unobserve(target);
    };
  }, [options, hasIntersected]);

  return { targetRef, isIntersecting, hasIntersected };
};

// Hook for measuring component performance
export const usePerformanceMonitor = (componentName: string) => {
  const renderCount = useRef(0);
  const startTime = useRef<number>(0);

  useEffect(() => {
    renderCount.current += 1;
    startTime.current = performance.now();

    return () => {
      const endTime = performance.now();
      const renderTime = endTime - startTime.current;
      
      if (process.env.NODE_ENV === 'development') {
        console.log(`${componentName} render #${renderCount.current}: ${renderTime.toFixed(2)}ms`);
      }
    };
  });

  const logPerformance = useCallback((operation: string, startTime: number) => {
    const endTime = performance.now();
    const duration = endTime - startTime;
    
    if (process.env.NODE_ENV === 'development') {
      console.log(`${componentName} ${operation}: ${duration.toFixed(2)}ms`);
    }
    
    return duration;
  }, [componentName]);

  return { renderCount: renderCount.current, logPerformance };
};

// Hook for virtual scrolling
export const useVirtualScroll = <T>(
  items: T[],
  itemHeight: number,
  containerHeight: number,
  overscan: number = 5
) => {
  const [scrollTop, setScrollTop] = useState(0);
  const scrollElementRef = useRef<HTMLDivElement>(null);

  const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
  const endIndex = Math.min(
    items.length - 1,
    Math.ceil((scrollTop + containerHeight) / itemHeight) + overscan
  );

  const visibleItems = items.slice(startIndex, endIndex + 1).map((item, index) => ({
    item,
    index: startIndex + index,
  }));

  const totalHeight = items.length * itemHeight;
  const offsetY = startIndex * itemHeight;

  const handleScroll = useThrottle((event: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(event.currentTarget.scrollTop);
  }, 16); // ~60fps

  return {
    scrollElementRef,
    visibleItems,
    totalHeight,
    offsetY,
    handleScroll,
  };
};

// Hook for memoizing expensive calculations
export const useMemoizedValue = <T>(
  factory: () => T,
  deps: React.DependencyList
): T => {
  const memoizedValue = useRef<T>();
  const depsRef = useRef<React.DependencyList>();

  const hasChanged = !depsRef.current || 
    deps.length !== depsRef.current.length ||
    deps.some((dep, index) => dep !== depsRef.current![index]);

  if (hasChanged) {
    memoizedValue.current = factory();
    depsRef.current = deps;
  }

  return memoizedValue.current!;
};

// Hook for lazy loading components
export const useLazyComponent = <T extends React.ComponentType<any>>(
  importFunc: () => Promise<{ default: T }>
) => {
  const [Component, setComponent] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  const loadComponent = useCallback(async () => {
    if (Component) return;

    setLoading(true);
    setError(null);

    try {
      const module = await importFunc();
      setComponent(() => module.default);
    } catch (err) {
      setError(err as Error);
    } finally {
      setLoading(false);
    }
  }, [importFunc, Component]);

  return { Component, loading, error, loadComponent };
};

// Hook for optimizing re-renders
export const useRenderOptimization = () => {
  const renderCount = useRef(0);
  const lastProps = useRef<any>(null);
  const lastState = useRef<any>(null);

  const trackRender = useCallback((props?: any, state?: any) => {
    renderCount.current += 1;
    
    if (process.env.NODE_ENV === 'development') {
      const propsChanged = JSON.stringify(props) !== JSON.stringify(lastProps.current);
      const stateChanged = JSON.stringify(state) !== JSON.stringify(lastState.current);
      
      console.log(`Render #${renderCount.current}`, {
        propsChanged,
        stateChanged,
        props: propsChanged ? { old: lastProps.current, new: props } : 'unchanged',
        state: stateChanged ? { old: lastState.current, new: state } : 'unchanged',
      });
    }
    
    lastProps.current = props;
    lastState.current = state;
  }, []);

  return { renderCount: renderCount.current, trackRender };
};

// Hook for batch updates
export const useBatchedUpdates = () => {
  const updates = useRef<(() => void)[]>([]);
  const timeoutRef = useRef<number>();

  const batchUpdate = useCallback((updateFn: () => void) => {
    updates.current.push(updateFn);
    
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    
    timeoutRef.current = setTimeout(() => {
      const currentUpdates = [...updates.current];
      updates.current = [];
      
      currentUpdates.forEach(update => update());
    }, 0);
  }, []);

  return { batchUpdate };
};

// Hook for memory usage monitoring
export const useMemoryMonitor = () => {
  const [memoryInfo, setMemoryInfo] = useState<{
    usedJSHeapSize?: number;
    totalJSHeapSize?: number;
    jsHeapSizeLimit?: number;
  }>({});

  useEffect(() => {
    const updateMemoryInfo = () => {
      if ('memory' in performance) {
        const memory = (performance as any).memory;
        setMemoryInfo({
          usedJSHeapSize: memory.usedJSHeapSize,
          totalJSHeapSize: memory.totalJSHeapSize,
          jsHeapSizeLimit: memory.jsHeapSizeLimit,
        });
      }
    };

    updateMemoryInfo();
    const interval = setInterval(updateMemoryInfo, 5000);

    return () => clearInterval(interval);
  }, []);

  return memoryInfo;
};

// Comprehensive performance hook
export const usePerformance = () => {
  const memoryInfo = useMemoryMonitor();
  const { renderCount, trackRender } = useRenderOptimization();
  const { batchUpdate } = useBatchedUpdates();

  return {
    memoryInfo,
    renderCount,
    trackRender,
    batchUpdate,
    debounce: useDebounce,
    throttle: useThrottle,
    intersectionObserver: useIntersectionObserver,
    virtualScroll: useVirtualScroll,
    memoizedValue: useMemoizedValue,
    lazyComponent: useLazyComponent,
  };
};
