import { useMemo } from 'react';

export interface Theme {
  colors: {
    primary: string;
    secondary: string;
    accent: string;
    background: string;
    surface: string;
    border: string;
    text: {
      primary: string;
      secondary: string;
      muted: string;
    };
    status: {
      success: string;
      warning: string;
      error: string;
      info: string;
    };
  };
  spacing: {
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
  };
  borderRadius: {
    sm: string;
    md: string;
    lg: string;
    xl: string;
  };
  shadows: {
    sm: string;
    md: string;
    lg: string;
    xl: string;
  };
}

const defaultTheme: Theme = {
  colors: {
    primary: 'blue-600',
    secondary: 'gray-600',
    accent: 'indigo-600',
    background: 'white',
    surface: 'gray-50',
    border: 'gray-200',
    text: {
      primary: 'gray-900',
      secondary: 'gray-600',
      muted: 'gray-400',
    },
    status: {
      success: 'green-600',
      warning: 'yellow-600',
      error: 'red-600',
      info: 'blue-600',
    },
  },
  spacing: {
    xs: '0.25rem',
    sm: '0.5rem',
    md: '1rem',
    lg: '1.5rem',
    xl: '2rem',
  },
  borderRadius: {
    sm: '0.25rem',
    md: '0.375rem',
    lg: '0.5rem',
    xl: '0.75rem',
  },
  shadows: {
    sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
    md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
    lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
    xl: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
  },
};

export const useTheme = (customTheme?: Partial<Theme>): Theme => {
  return useMemo(() => {
    if (!customTheme) return defaultTheme;
    
    return {
      ...defaultTheme,
      ...customTheme,
      colors: {
        ...defaultTheme.colors,
        ...customTheme.colors,
        text: {
          ...defaultTheme.colors.text,
          ...customTheme.colors?.text,
        },
        status: {
          ...defaultTheme.colors.status,
          ...customTheme.colors?.status,
        },
      },
      spacing: {
        ...defaultTheme.spacing,
        ...customTheme.spacing,
      },
      borderRadius: {
        ...defaultTheme.borderRadius,
        ...customTheme.borderRadius,
      },
      shadows: {
        ...defaultTheme.shadows,
        ...customTheme.shadows,
      },
    };
  }, [customTheme]);
};
