import { useEffect, useRef, useCallback } from 'react';

// Hook for managing focus
export const useFocusManagement = () => {
  const focusableElementsSelector = [
    'a[href]',
    'button:not([disabled])',
    'textarea:not([disabled])',
    'input:not([disabled])',
    'select:not([disabled])',
    '[tabindex]:not([tabindex="-1"])',
    '[contenteditable="true"]'
  ].join(', ');

  const trapFocus = useCallback((container: HTMLElement) => {
    const focusableElements = container.querySelectorAll(focusableElementsSelector);
    const firstElement = focusableElements[0] as HTMLElement;
    const lastElement = focusableElements[focusableElements.length - 1] as HTMLElement;

    const handleTabKey = (e: KeyboardEvent) => {
      if (e.key !== 'Tab') return;

      if (e.shiftKey) {
        if (document.activeElement === firstElement) {
          e.preventDefault();
          lastElement?.focus();
        }
      } else {
        if (document.activeElement === lastElement) {
          e.preventDefault();
          firstElement?.focus();
        }
      }
    };

    container.addEventListener('keydown', handleTabKey);
    firstElement?.focus();

    return () => {
      container.removeEventListener('keydown', handleTabKey);
    };
  }, [focusableElementsSelector]);

  const restoreFocus = useCallback((element: HTMLElement | null) => {
    if (element && typeof element.focus === 'function') {
      element.focus();
    }
  }, []);

  return { trapFocus, restoreFocus };
};

// Hook for keyboard navigation
export const useKeyboardNavigation = (
  items: any[],
  onSelect?: (index: number) => void,
  options: {
    loop?: boolean;
    orientation?: 'horizontal' | 'vertical';
    disabled?: boolean;
  } = {}
) => {
  const { loop = true, orientation = 'vertical', disabled = false } = options;
  const currentIndex = useRef(0);

  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    if (disabled || items.length === 0) return;

    const isVertical = orientation === 'vertical';
    const nextKey = isVertical ? 'ArrowDown' : 'ArrowRight';
    const prevKey = isVertical ? 'ArrowUp' : 'ArrowLeft';

    switch (event.key) {
      case nextKey:
        event.preventDefault();
        currentIndex.current = loop 
          ? (currentIndex.current + 1) % items.length
          : Math.min(currentIndex.current + 1, items.length - 1);
        onSelect?.(currentIndex.current);
        break;

      case prevKey:
        event.preventDefault();
        currentIndex.current = loop
          ? currentIndex.current === 0 ? items.length - 1 : currentIndex.current - 1
          : Math.max(currentIndex.current - 1, 0);
        onSelect?.(currentIndex.current);
        break;

      case 'Home':
        event.preventDefault();
        currentIndex.current = 0;
        onSelect?.(currentIndex.current);
        break;

      case 'End':
        event.preventDefault();
        currentIndex.current = items.length - 1;
        onSelect?.(currentIndex.current);
        break;

      case 'Enter':
      case ' ':
        event.preventDefault();
        onSelect?.(currentIndex.current);
        break;
    }
  }, [items, onSelect, loop, orientation, disabled]);

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [handleKeyDown]);

  return { currentIndex: currentIndex.current };
};

// Hook for screen reader announcements
export const useScreenReader = () => {
  const announceRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    // Create live region for announcements
    const liveRegion = document.createElement('div');
    liveRegion.setAttribute('aria-live', 'polite');
    liveRegion.setAttribute('aria-atomic', 'true');
    liveRegion.style.position = 'absolute';
    liveRegion.style.left = '-10000px';
    liveRegion.style.width = '1px';
    liveRegion.style.height = '1px';
    liveRegion.style.overflow = 'hidden';
    
    document.body.appendChild(liveRegion);
    announceRef.current = liveRegion;

    return () => {
      if (announceRef.current) {
        document.body.removeChild(announceRef.current);
      }
    };
  }, []);

  const announce = useCallback((message: string, priority: 'polite' | 'assertive' = 'polite') => {
    if (announceRef.current) {
      announceRef.current.setAttribute('aria-live', priority);
      announceRef.current.textContent = message;
      
      // Clear after announcement
      setTimeout(() => {
        if (announceRef.current) {
          announceRef.current.textContent = '';
        }
      }, 1000);
    }
  }, []);

  return { announce };
};

// Hook for reduced motion preferences
export const useReducedMotion = () => {
  const prefersReducedMotion = useRef(false);

  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    prefersReducedMotion.current = mediaQuery.matches;

    const handleChange = (event: MediaQueryListEvent) => {
      prefersReducedMotion.current = event.matches;
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  return prefersReducedMotion.current;
};

// Hook for high contrast mode detection
export const useHighContrast = () => {
  const prefersHighContrast = useRef(false);

  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-contrast: high)');
    prefersHighContrast.current = mediaQuery.matches;

    const handleChange = (event: MediaQueryListEvent) => {
      prefersHighContrast.current = event.matches;
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  return prefersHighContrast.current;
};

// Hook for managing ARIA attributes
export const useAriaAttributes = () => {
  const generateId = useCallback((prefix: string = 'aria') => {
    return `${prefix}-${Math.random().toString(36).substr(2, 9)}`;
  }, []);

  const createAriaProps = useCallback((config: {
    label?: string;
    labelledBy?: string;
    describedBy?: string;
    expanded?: boolean;
    selected?: boolean;
    disabled?: boolean;
    required?: boolean;
    invalid?: boolean;
    live?: 'polite' | 'assertive' | 'off';
    role?: string;
  }) => {
    const props: Record<string, any> = {};

    if (config.label) props['aria-label'] = config.label;
    if (config.labelledBy) props['aria-labelledby'] = config.labelledBy;
    if (config.describedBy) props['aria-describedby'] = config.describedBy;
    if (config.expanded !== undefined) props['aria-expanded'] = config.expanded;
    if (config.selected !== undefined) props['aria-selected'] = config.selected;
    if (config.disabled !== undefined) props['aria-disabled'] = config.disabled;
    if (config.required !== undefined) props['aria-required'] = config.required;
    if (config.invalid !== undefined) props['aria-invalid'] = config.invalid;
    if (config.live) props['aria-live'] = config.live;
    if (config.role) props['role'] = config.role;

    return props;
  }, []);

  return { generateId, createAriaProps };
};

// Hook for skip links
export const useSkipLinks = () => {
  const skipLinksRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    const skipLinks = document.createElement('div');
    skipLinks.className = 'skip-links';
    skipLinks.style.position = 'absolute';
    skipLinks.style.top = '-40px';
    skipLinks.style.left = '6px';
    skipLinks.style.zIndex = '1000';
    skipLinks.style.transition = 'top 0.3s';

    const mainContentLink = document.createElement('a');
    mainContentLink.href = '#main-content';
    mainContentLink.textContent = 'Skip to main content';
    mainContentLink.className = 'skip-link';
    mainContentLink.style.background = '#000';
    mainContentLink.style.color = '#fff';
    mainContentLink.style.padding = '8px';
    mainContentLink.style.textDecoration = 'none';
    mainContentLink.style.borderRadius = '4px';

    mainContentLink.addEventListener('focus', () => {
      skipLinks.style.top = '6px';
    });

    mainContentLink.addEventListener('blur', () => {
      skipLinks.style.top = '-40px';
    });

    skipLinks.appendChild(mainContentLink);
    document.body.insertBefore(skipLinks, document.body.firstChild);
    skipLinksRef.current = skipLinks;

    return () => {
      if (skipLinksRef.current) {
        document.body.removeChild(skipLinksRef.current);
      }
    };
  }, []);

  return skipLinksRef.current;
};

// Comprehensive accessibility hook
export const useAccessibility = () => {
  const { trapFocus, restoreFocus } = useFocusManagement();
  const { announce } = useScreenReader();
  const { generateId, createAriaProps } = useAriaAttributes();
  const prefersReducedMotion = useReducedMotion();
  const prefersHighContrast = useHighContrast();

  return {
    trapFocus,
    restoreFocus,
    announce,
    generateId,
    createAriaProps,
    prefersReducedMotion,
    prefersHighContrast,
  };
};
