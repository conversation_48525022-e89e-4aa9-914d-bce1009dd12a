import { useState, useCallback, useRef, useReducer, useMemo } from 'react';
import { nanoid } from 'nanoid';
import { ContentState, Row, Column, Block, BuilderSettings } from '../types';
import { generateHtml } from '../utils/htmlGenerator';

type State = {
  viewport: 'mobile' | 'tablet' | 'desktop';
  showCode: boolean;
  zoom: number;
  leftSidebarOpen: boolean;
  rightSidebarOpen: boolean;
};

type Action = 
  | { type: 'SET_VIEWPORT'; payload: 'mobile' | 'tablet' | 'desktop' }
  | { type: 'TOGGLE_CODE'; payload?: boolean }
  | { type: 'SET_ZOOM'; payload: number }
  | { type: 'TOGGLE_LEFT_SIDEBAR'; payload?: boolean }
  | { type: 'TOGGLE_RIGHT_SIDEBAR'; payload?: boolean };

function reducer(state: State, action: Action): State {
  switch (action.type) {
    case 'SET_VIEWPORT':
      return { ...state, viewport: action.payload };
    case 'TOGGLE_CODE':
      return { ...state, showCode: action.payload ?? !state.showCode };
    case 'SET_ZOOM':
      return { ...state, zoom: action.payload };
    case 'TOGGLE_LEFT_SIDEBAR':
      return { ...state, leftSidebarOpen: action.payload ?? !state.leftSidebarOpen };
    case 'TOGGLE_RIGHT_SIDEBAR':
      return { ...state, rightSidebarOpen: action.payload ?? !state.rightSidebarOpen };
    default:
      return state;
  }
}

export function useContentBuilderState(initialState: Partial<State> = {}) {
  return useReducer(reducer, {
    viewport: 'desktop',
    showCode: false,
    zoom: 100,
    leftSidebarOpen: true,
    rightSidebarOpen: true,
    ...initialState
  });
}

export interface UseContentBuilderOptions {
  initialContent?: ContentState;
  maxColumns?: number;
  onChange?: (content: ContentState) => void;
}

export interface UseContentBuilderReturn {
  // State
  content: ContentState;
  selectedBlockId: string | null;
  isDirty: boolean;
  
  // Content actions
  addRow: (index?: number) => void;
  removeRow: (rowId: string) => void;
  addColumn: (rowId: string, size: number, index?: number) => void;
  removeColumn: (columnId: string) => void;
  addBlock: (columnId: string, blockType: string, index?: number) => void;
  removeBlock: (blockId: string) => void;
  updateBlock: (blockId: string, updates: Partial<Block>) => void;
  moveBlock: (blockId: string, targetColumnId: string, targetIndex: number) => void;
  
  // Selection
  selectBlock: (blockId: string | null) => void;
  
  // Content operations
  duplicateBlock: (blockId: string) => void;
  clearContent: () => void;
  
  // History
  canUndo: boolean;
  canRedo: boolean;
  undo: () => void;
  redo: () => void;
  
  // Export
  exportHtml: () => string;
  exportJson: () => string;

  // Utilities
  findBlockById: (blockId: string) => Block | null;
}

const createDefaultSettings = (maxColumns = 12): BuilderSettings => ({
  maxColumns,
  containerMaxWidth: '1200px',
  gutterWidth: '1rem',
  breakpoints: {
    mobile: 768,
    tablet: 1024,
    desktop: 1200,
  },
  defaultRowPadding: '1rem 0',
  defaultColumnPadding: '0.5rem',
  enabledFeatures: {
    dragDrop: true,
    responsivePreview: true,
    codeExport: true,
    history: true,
    templates: true,
  },
  classPrefix: 'cb-',
});

const createEmptyContent = (maxColumns = 12): ContentState => ({
  rows: [],
  settings: createDefaultSettings(maxColumns),
  metadata: {
    createdAt: new Date(),
    updatedAt: new Date(),
    version: '1.0.0',
  },
});

export const useContentBuilder = (options: UseContentBuilderOptions = {}): UseContentBuilderReturn => {
  const { initialContent, maxColumns = 12, onChange } = options;
  
  const [content, setContent] = useState<ContentState>(
    initialContent || createEmptyContent(maxColumns)
  );
  const [selectedBlockId, setSelectedBlockId] = useState<string | null>(null);
  const [history, setHistory] = useState<ContentState[]>([content]);
  const [historyIndex, setHistoryIndex] = useState(0);
  const [isDirty, setIsDirty] = useState(false);
  
  const historyRef = useRef({ history, historyIndex });
  historyRef.current = { history, historyIndex };
  
  const updateContent = useCallback((newContent: ContentState) => {
    setContent(newContent);
    setIsDirty(true);
    
    // Add to history
    const newHistory = historyRef.current.history.slice(0, historyRef.current.historyIndex + 1);
    newHistory.push(newContent);
    setHistory(newHistory);
    setHistoryIndex(newHistory.length - 1);
    
    onChange?.(newContent);
  }, [onChange]);
  
  const addRow = useCallback((index?: number) => {
    const newRow: Row = {
      id: nanoid(),
      columns: [],
      styles: {},
      attributes: {},
    };
    
    const newContent = { ...content };
    if (typeof index === 'number') {
      newContent.rows.splice(index, 0, newRow);
    } else {
      newContent.rows.push(newRow);
    }
    
    updateContent(newContent);
  }, [content, updateContent]);
  
  const removeRow = useCallback((rowId: string) => {
    const newContent = {
      ...content,
      rows: content.rows.filter(row => row.id !== rowId),
    };
    updateContent(newContent);
  }, [content, updateContent]);
  
  const addColumn = useCallback((rowId: string, size: number, index?: number) => {
    const newColumn: Column = {
      id: nanoid(),
      size,
      blocks: [],
      styles: {},
      attributes: {},
    };
    
    const newContent = { ...content };
    const rowIndex = newContent.rows.findIndex(row => row.id === rowId);
    if (rowIndex !== -1) {
      if (typeof index === 'number') {
        newContent.rows[rowIndex].columns.splice(index, 0, newColumn);
      } else {
        newContent.rows[rowIndex].columns.push(newColumn);
      }
    }
    
    updateContent(newContent);
  }, [content, updateContent]);
  
  const removeColumn = useCallback((columnId: string) => {
    const newContent = { ...content };
    for (const row of newContent.rows) {
      row.columns = row.columns.filter(col => col.id !== columnId);
    }
    updateContent(newContent);
  }, [content, updateContent]);
  
  const addBlock = useCallback((columnId: string, blockType: string, index?: number) => {
    const newBlock: Block = {
      id: nanoid(),
      type: blockType,
      content: {},
      styles: {},
      attributes: {},
    };
    
    const newContent = { ...content };
    for (const row of newContent.rows) {
      const column = row.columns.find(col => col.id === columnId);
      if (column) {
        if (typeof index === 'number') {
          column.blocks.splice(index, 0, newBlock);
        } else {
          column.blocks.push(newBlock);
        }
        break;
      }
    }
    
    updateContent(newContent);
  }, [content, updateContent]);
  
  const removeBlock = useCallback((blockId: string) => {
    const newContent = { ...content };
    for (const row of newContent.rows) {
      for (const column of row.columns) {
        column.blocks = column.blocks.filter(block => block.id !== blockId);
      }
    }
    
    if (selectedBlockId === blockId) {
      setSelectedBlockId(null);
    }
    
    updateContent(newContent);
  }, [content, selectedBlockId, updateContent]);
  
  const updateBlock = useCallback((blockId: string, updates: Partial<Block>) => {
    const newContent = { ...content };
    for (const row of newContent.rows) {
      for (const column of row.columns) {
        const blockIndex = column.blocks.findIndex(block => block.id === blockId);
        if (blockIndex !== -1) {
          column.blocks[blockIndex] = { ...column.blocks[blockIndex], ...updates };
          updateContent(newContent);
          return;
        }
      }
    }
  }, [content, updateContent]);
  
  const moveBlock = useCallback((blockId: string, targetColumnId: string, targetIndex: number) => {
    const newContent = { ...content };
    let blockToMove: Block | null = null;
    
    // Find and remove the block
    for (const row of newContent.rows) {
      for (const column of row.columns) {
        const blockIndex = column.blocks.findIndex(block => block.id === blockId);
        if (blockIndex !== -1) {
          blockToMove = column.blocks.splice(blockIndex, 1)[0];
          break;
        }
      }
      if (blockToMove) break;
    }
    
    // Add the block to the target column
    if (blockToMove) {
      for (const row of newContent.rows) {
        const targetColumn = row.columns.find(col => col.id === targetColumnId);
        if (targetColumn) {
          targetColumn.blocks.splice(targetIndex, 0, blockToMove);
          break;
        }
      }
    }
    
    updateContent(newContent);
  }, [content, updateContent]);
  
  const selectBlock = useCallback((blockId: string | null) => {
    setSelectedBlockId(blockId);
  }, []);
  
  const duplicateBlock = useCallback((blockId: string) => {
    const newContent = { ...content };
    for (const row of newContent.rows) {
      for (const column of row.columns) {
        const blockIndex = column.blocks.findIndex(block => block.id === blockId);
        if (blockIndex !== -1) {
          const originalBlock = column.blocks[blockIndex];
          const duplicatedBlock: Block = {
            ...originalBlock,
            id: nanoid(),
          };
          column.blocks.splice(blockIndex + 1, 0, duplicatedBlock);
          updateContent(newContent);
          return;
        }
      }
    }
  }, [content, updateContent]);
  
  const clearContent = useCallback(() => {
    updateContent(createEmptyContent(maxColumns));
    setSelectedBlockId(null);
  }, [maxColumns, updateContent]);
  
  const undo = useCallback(() => {
    if (historyIndex > 0) {
      const newIndex = historyIndex - 1;
      setContent(history[newIndex]);
      setHistoryIndex(newIndex);
      setIsDirty(true);
      onChange?.(history[newIndex]);
    }
  }, [history, historyIndex, onChange]);
  
  const redo = useCallback(() => {
    if (historyIndex < history.length - 1) {
      const newIndex = historyIndex + 1;
      setContent(history[newIndex]);
      setHistoryIndex(newIndex);
      setIsDirty(true);
      onChange?.(history[newIndex]);
    }
  }, [history, historyIndex, onChange]);
  
  const exportHtml = useCallback(() => {
    return generateHtml(content, {
      classPrefix: content.settings.classPrefix,
      minify: false,
      includeComments: true,
    });
  }, [content]);
  
  const exportJson = useCallback(() => {
    return JSON.stringify(content, null, 2);
  }, [content]);
  
  const blockMap = useMemo(() => {
    const map = new Map<string, Block>();
    
    content.rows.forEach(row => {
      row.columns.forEach(column => {
        column.blocks.forEach(block => {
          map.set(block.id, block);
        });
      });
    });
    
    return map;
  }, [content.rows]);

  const findBlockById = useCallback((blockId: string): Block | null => {
    return blockMap.get(blockId) || null;
  }, [blockMap]);
  
  return {
    // State
    content,
    selectedBlockId,
    isDirty,
    
    // Content actions
    addRow,
    removeRow,
    addColumn,
    removeColumn,
    addBlock,
    removeBlock,
    updateBlock,
    moveBlock,
    
    // Selection
    selectBlock,
    
    // Content operations
    duplicateBlock,
    clearContent,
    
    // History
    canUndo: historyIndex > 0,
    canRedo: historyIndex < history.length - 1,
    undo,
    redo,
    
    // Export
    exportHtml,
    exportJson,

    // Utilities
    findBlockById,
  };
};
