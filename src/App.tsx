import React from 'react';
import { ContentBuilder } from './components/ContentBuilder';
import { ContentState } from './types';

function App() {
  const handleSave = (content: ContentState) => {
    console.log('Saving content:', content);
  };

  const handleExport = (html: string, css: string) => {
    console.log('Exporting:', { html, css });
  };

  return (
    <div className="h-screen">
      <ContentBuilder
        showSidebar={true}
        showToolbar={true}
        enableResponsivePreview={true}
        enableCodeExport={true}
        onSave={handleSave}
        onExport={handleExport}
        onChange={(content) => console.log('Content changed:', content)}
      />
    </div>
  );
}

export default App;
