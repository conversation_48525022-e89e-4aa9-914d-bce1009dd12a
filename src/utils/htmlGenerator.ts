import { ContentState, Row, Column, Block } from '../types';

export interface HtmlGeneratorOptions {
  classPrefix?: string;
  minify?: boolean;
  includeComments?: boolean;
}

export class HtmlGenerator {
  private options: Required<HtmlGeneratorOptions>;

  constructor(options: HtmlGeneratorOptions = {}) {
    this.options = {
      classPrefix: 'cb-',
      minify: false,
      includeComments: true,
      ...options,
    };
  }

  generate(content: ContentState): string {
    const html = this.generateHtml(content);
    return this.options.minify ? this.minifyHtml(html) : html;
  }

  private generateHtml(content: ContentState): string {
    const rows = content.rows.map(row => this.generateRow(row)).join('\n');
    
    return `
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${content.metadata.title || 'Generated Content'}</title>
  <script src="https://cdn.tailwindcss.com"></script>
  ${this.generateCustomStyles(content)}
</head>
<body>
  <div class="container mx-auto px-4">
    ${this.options.includeComments ? '<!-- Generated by Content Builder -->' : ''}
    ${rows}
  </div>
</body>
</html>`.trim();
  }

  private generateRow(row: Row): string {
    const columns = row.columns.map(column => this.generateColumn(column)).join('\n    ');
    const rowClasses = this.getRowClasses();
    const rowStyles = this.generateInlineStyles(row.styles);
    
    return `
  <div class="${rowClasses}"${rowStyles}>
    ${columns}
  </div>`.trim();
  }

  private generateColumn(column: Column): string {
    const blocks = column.blocks.map(block => this.generateBlock(block)).join('\n      ');
    const columnClasses = this.getColumnClasses(column);
    const columnStyles = this.generateInlineStyles(column.styles);
    
    return `
    <div class="${columnClasses}"${columnStyles}>
      ${blocks}
    </div>`.trim();
  }

  private generateBlock(block: Block): string {
    switch (block.type) {
      case 'text':
        return this.generateTextBlock(block);
      case 'image':
        return this.generateImageBlock(block);
      case 'button':
        return this.generateButtonBlock(block);
      case 'divider':
        return this.generateDividerBlock(block);
      case 'spacer':
        return this.generateSpacerBlock(block);
      default:
        return `<!-- Unknown block type: ${block.type} -->`;
    }
  }

  private generateTextBlock(block: Block): string {
    const { text, tag } = block.content;
    const styles = this.generateInlineStyles(block.styles);
    const attributes = this.generateAttributes(block.attributes);
    
    return `<${tag}${attributes}${styles}>${text}</${tag}>`;
  }

  private generateImageBlock(block: Block): string {
    const { src, alt, caption, link } = block.content;
    const styles = this.generateInlineStyles(block.styles);
    const attributes = this.generateAttributes(block.attributes);
    
    let imageElement = `<img src="${src}" alt="${alt}" class="max-w-full h-auto"${attributes}${styles}>`;
    
    if (link) {
      imageElement = `<a href="${link}">${imageElement}</a>`;
    }
    
    if (caption) {
      imageElement += `\n        <p class="text-sm text-gray-600 mt-2">${caption}</p>`;
    }
    
    return imageElement;
  }

  private generateButtonBlock(block: Block): string {
    const { text, link, target, variant } = block.content;
    const styles = this.generateInlineStyles(block.styles);
    const attributes = this.generateAttributes(block.attributes);
    const buttonClasses = this.getButtonClasses(variant);
    
    if (link) {
      return `<a href="${link}" target="${target || '_self'}" class="${buttonClasses}"${attributes}${styles}>${text}</a>`;
    } else {
      return `<button class="${buttonClasses}"${attributes}${styles}>${text}</button>`;
    }
  }

  private generateDividerBlock(block: Block): string {
    const { style, thickness, color } = block.content;
    const borderStyle = style || 'solid';
    const borderThickness = thickness || 1;
    const borderColor = color || '#e5e7eb';
    
    const styles = this.generateInlineStyles({
      ...block.styles,
      borderTop: `${borderThickness}px ${borderStyle} ${borderColor}`,
      width: '100%',
    });
    const attributes = this.generateAttributes(block.attributes);
    
    return `<hr class="border-0"${attributes}${styles}>`;
  }

  private generateSpacerBlock(block: Block): string {
    const { height } = block.content;
    const styles = this.generateInlineStyles({
      ...block.styles,
      height: `${height || 50}px`,
    });
    const attributes = this.generateAttributes(block.attributes);
    
    return `<div${attributes}${styles}></div>`;
  }

  private getRowClasses(): string {
    return 'grid grid-cols-12 gap-4 mb-4';
  }

  private getColumnClasses(column: Column): string {
    return `col-span-${column.size}`;
  }

  private getButtonClasses(variant: string): string {
    const baseClasses = 'inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors';
    
    switch (variant) {
      case 'primary':
        return `${baseClasses} text-white bg-blue-600 hover:bg-blue-700 focus:ring-blue-500`;
      case 'secondary':
        return `${baseClasses} text-gray-700 bg-white border-gray-300 hover:bg-gray-50 focus:ring-blue-500`;
      case 'outline':
        return `${baseClasses} text-blue-700 bg-transparent border-blue-600 hover:bg-blue-50 focus:ring-blue-500`;
      case 'ghost':
        return `${baseClasses} text-gray-700 bg-transparent hover:bg-gray-50 focus:ring-gray-500`;
      default:
        return `${baseClasses} text-gray-700 bg-gray-100 hover:bg-gray-200 focus:ring-gray-500`;
    }
  }

  private generateCustomStyles(content: ContentState): string {
    if (content.settings.customCss) {
      return `<style>
${content.settings.customCss}
  </style>`;
    }
    return '';
  }

  private generateInlineStyles(styles: any): string {
    if (!styles) return '';
    
    const styleString = Object.entries(styles)
      .filter(([key, value]) => value && key !== 'customCss' && key !== 'mobile' && key !== 'tablet' && key !== 'desktop')
      .map(([key, value]) => `${this.camelToKebab(key)}: ${value}`)
      .join('; ');
    
    return styleString ? ` style="${styleString}"` : '';
  }

  private generateAttributes(attributes: any): string {
    if (!attributes) return '';
    
    return Object.entries(attributes)
      .filter(([, value]) => value)
      .map(([key, value]) => ` ${key}="${value}"`)
      .join('');
  }

  private camelToKebab(str: string): string {
    return str.replace(/[A-Z]/g, letter => `-${letter.toLowerCase()}`);
  }

  private minifyHtml(html: string): string {
    return html
      .replace(/\s+/g, ' ')
      .replace(/>\s+</g, '><')
      .trim();
  }
}

// Factory function for convenience
export const generateHtml = (content: ContentState, options?: HtmlGeneratorOptions): string => {
  const generator = new HtmlGenerator(options);
  return generator.generate(content);
};
