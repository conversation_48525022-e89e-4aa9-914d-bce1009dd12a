// Layout and structure type definitions
import { Block } from './blocks';

export interface ContentState {
  rows: Row[];
  settings: BuilderSettings;
  metadata: ContentMetadata;
}

export interface Row {
  id: string;
  columns: Column[];
  styles: RowStyles;
  attributes: Record<string, string>;
}

export interface Column {
  id: string;
  size: number; // 1-12 for grid system
  blocks: Block[];
  styles: ColumnStyles;
  attributes: Record<string, string>;
}

export interface RowStyles {
  backgroundColor?: string;
  backgroundImage?: string;
  padding?: string;
  margin?: string;
  minHeight?: string;
  justifyContent?: 'flex-start' | 'center' | 'flex-end' | 'space-between' | 'space-around';
  alignItems?: 'flex-start' | 'center' | 'flex-end' | 'stretch';
  customCss?: string;
  
  // Responsive styles
  mobile?: Partial<RowStyles>;
  tablet?: Partial<RowStyles>;
  desktop?: Partial<RowStyles>;
}

export interface ColumnStyles {
  backgroundColor?: string;
  padding?: string;
  margin?: string;
  border?: string;
  borderRadius?: string;
  customCss?: string;
  
  // Responsive column sizes
  mobile?: number;
  tablet?: number;
  desktop?: number;
}

export interface BuilderSettings {
  // Grid system
  maxColumns: number;
  containerMaxWidth: string;
  gutterWidth: string;
  
  // Breakpoints
  breakpoints: {
    mobile: number;
    tablet: number;
    desktop: number;
  };
  
  // Default styles
  defaultRowPadding: string;
  defaultColumnPadding: string;
  
  // Features
  enabledFeatures: {
    dragDrop: boolean;
    responsivePreview: boolean;
    codeExport: boolean;
    history: boolean;
    templates: boolean;
  };
  
  // Output settings
  classPrefix: string;
  
  // Custom CSS
  customCss?: string;
}

export interface ContentMetadata {
  title?: string;
  description?: string;
  author?: string;
  createdAt: Date;
  updatedAt: Date;
  version: string;
  tags?: string[];
}
