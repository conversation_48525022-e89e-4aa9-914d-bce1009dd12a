// Block-related type definitions
export interface Block {
  id: string;
  type: string;
  content: any;
  styles: BlockStyles;
  attributes: BlockAttributes;
  validation?: ValidationRule[];
}

export interface BlockType {
  name: string;
  category: string;
  icon: React.ComponentType;
  defaultProps: Partial<Block>;
  editable: boolean;
  resizable: boolean;
  configurable: boolean;
}

export interface BlockStyles {
  // Layout styles
  width?: string;
  height?: string;
  padding?: string;
  margin?: string;
  
  // Text styles
  fontSize?: string;
  fontWeight?: string;
  fontFamily?: string;
  color?: string;
  textAlign?: 'left' | 'center' | 'right' | 'justify';
  lineHeight?: string;
  
  // Background styles
  backgroundColor?: string;
  backgroundImage?: string;
  backgroundSize?: string;
  backgroundPosition?: string;
  backgroundRepeat?: string;
  
  // Border styles
  border?: string;
  borderRadius?: string;
  boxShadow?: string;
  
  // Responsive styles
  mobile?: Partial<BlockStyles>;
  tablet?: Partial<BlockStyles>;
  desktop?: Partial<BlockStyles>;
  
  // Custom CSS
  customCss?: string;
}

export interface BlockAttributes {
  id?: string;
  className?: string;
  'data-*'?: string;
  role?: string;
  'aria-*'?: string;
  alt?: string;
  title?: string;
  href?: string;
  target?: string;
  rel?: string;
}

export interface ValidationRule {
  field: string;
  type: 'required' | 'min' | 'max' | 'pattern' | 'custom';
  value?: any;
  message: string;
  validator?: (value: any) => boolean;
}

// Specific block content types
export interface TextBlockContent {
  text: string;
  tag: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'p' | 'span' | 'div';
}

export interface ImageBlockContent {
  src: string;
  alt: string;
  caption?: string;
  link?: string;
}

export interface ButtonBlockContent {
  text: string;
  link?: string;
  target?: '_blank' | '_self';
  variant: 'primary' | 'secondary' | 'outline' | 'ghost';
}

export interface DividerBlockContent {
  style: 'solid' | 'dashed' | 'dotted';
  thickness: number;
  color: string;
}

export interface SpacerBlockContent {
  height: number;
}
