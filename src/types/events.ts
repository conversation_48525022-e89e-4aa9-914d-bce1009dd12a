// Event and callback type definitions
export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings?: ValidationWarning[];
}

export interface ValidationError {
  field: string;
  message: string;
  code?: string;
}

export interface ValidationWarning {
  field: string;
  message: string;
  code?: string;
}

// Drag and drop events
export interface DragEvent {
  blockType?: string;
  blockId?: string;
  sourceIndex?: number;
  targetIndex?: number;
  sourceColumnId?: string;
  targetColumnId?: string;
  sourceRowId?: string;
  targetRowId?: string;
}

export interface DropEvent extends DragEvent {
  position: {
    x: number;
    y: number;
  };
}

// Builder events
export interface BuilderEvent {
  type: 'block-add' | 'block-remove' | 'block-update' | 'block-select' | 'content-change' | 'save' | 'export';
  timestamp: Date;
  data?: any;
}

// History events for undo/redo
export interface HistoryEvent {
  type: 'undo' | 'redo';
  state: any; // Previous or next state
  timestamp: Date;
}

// Responsive events
export interface ViewportChangeEvent {
  viewport: 'mobile' | 'tablet' | 'desktop';
  width: number;
  height: number;
}
