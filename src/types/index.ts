// Main type exports for Content Builder
export * from './blocks';
export * from './layout';
export * from './events';

import { ContentState } from './layout';
import { Block, BlockType } from './blocks';

// Main ContentBuilder component props
export interface ContentBuilderProps {
  // Initial content state
  initialContent?: ContentState;
  
  // Styling options
  theme?: 'light' | 'dark' | 'custom';
  className?: string;
  style?: React.CSSProperties;
  
  // Feature toggles
  showSidebar?: boolean;
  showToolbar?: boolean;
  showPropertiesPanel?: boolean;
  enableResponsivePreview?: boolean;
  enableCodeExport?: boolean;
  
  // Grid system
  maxColumns?: number;
  
  // Block configuration
  enabledBlocks?: string[];
  customBlocks?: CustomBlock[];
  
  // Event handlers
  onChange?: (content: ContentState) => void;
  onSave?: (content: ContentState) => void;
  onExport?: (html: string, css: string) => void;
  onBlockSelect?: (blockId: string | null) => void;
  
  // Customization
  renderBlock?: (block: Block, props: BlockRenderProps) => React.ReactNode;
  renderToolbar?: (props: ToolbarProps) => React.ReactNode;
  renderSidebar?: (props: SidebarProps) => React.ReactNode;
}

// Additional component prop interfaces
export interface BlockRenderProps {
  block: Block;
  isSelected: boolean;
  isEditing: boolean;
  onUpdate: (updates: Partial<Block>) => void;
  onDelete: () => void;
}

export interface ToolbarProps {
  content: ContentState;
  selectedBlockId?: string;
  onUndo: () => void;
  onRedo: () => void;
  onSave: () => void;
  onPreview: () => void;
  onExport: () => void;
}

export interface SidebarProps {
  enabledBlocks: string[];
  customBlocks?: CustomBlock[];
  onBlockDragStart: (blockType: string) => void;
}

// Custom block definition
export interface CustomBlock {
  type: BlockType;
  component: React.ComponentType<BlockRenderProps>;
  configComponent?: React.ComponentType<{ block: Block; onChange: (block: Block) => void }>;
}

// Block template
export interface BlockTemplate {
  name: string;
  description: string;
  thumbnail?: string;
  blocks: Block[];
}
