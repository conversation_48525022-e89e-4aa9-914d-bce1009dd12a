// Main type exports for Content Builder
export * from './blocks';
export * from './layout';
export * from './events';

import { ContentState } from './layout';
import { Block, BlockType } from './blocks';

// Main ContentBuilder component props
export interface ContentBuilderProps {
  // Initial content state
  initialContent?: ContentState;
  
  // Styling options
  theme?: 'light' | 'dark' | 'custom';
  className?: string;
  style?: React.CSSProperties;
  
  // Feature toggles
  showSidebar?: boolean;
  showToolbar?: boolean;
  showPropertiesPanel?: boolean;
  enableResponsivePreview?: boolean;
  enableCodeExport?: boolean;
  
  // Grid system
  maxColumns?: number;
  
  // Block configuration
  enabledBlocks?: string[];
  customBlocks?: CustomBlock[];
  
  // Event handlers
  onChange?: (content: ContentState) => void;
  onSave?: (content: ContentState) => void;
  onExport?: (html: string, css: string) => void;
  onBlockSelect?: (blockId: string | null) => void;
  
  // Customization
  renderBlock?: (block: Block, props: BlockRenderProps) => React.ReactElement;
  renderToolbar?: (props: ToolbarProps) => React.ReactElement;
  renderSidebar?: (props: SidebarProps) => React.ReactElement;
  renderPropertiesPanel?: (props: PropertiesPanelProps) => React.ReactElement;
  renderCanvas?: (props: CanvasProps) => React.ReactElement;
}

// Additional component prop interfaces
export interface BlockRenderProps {
  block: Block;
  isSelected: boolean;
  isEditing: boolean;
  onUpdate: (updates: Partial<Block>) => void;
  onDelete: () => void;
}

export interface ToolbarProps {
  // History
  onUndo: () => void;
  canUndo: boolean;
  onRedo: () => void;
  canRedo: boolean;

  // Canvas Actions
  onAddRow: () => void;
  onClearContent: () => void;

  // Viewport
  currentViewport: 'mobile' | 'tablet' | 'desktop';
  onViewportChange: (viewport: 'mobile' | 'tablet' | 'desktop') => void;
  enableResponsivePreview: boolean;

  // Zoom
  currentZoom: number;
  onZoomChange: (zoom: number) => void;

  // Code & Export
  onShowCode: () => void;
  enableCodeExport: boolean;
  onSave: () => void;
  isDirty: boolean;
  onExport: () => void;

  theme?: 'light' | 'dark' | 'custom';
}

export interface SidebarProps {
  isOpen: boolean;
  onToggle: () => void;
  enabledBlocks?: string[];
  customBlocks?: CustomBlock[];
}

export interface PropertiesPanelProps {
  isOpen: boolean;
  onToggle: () => void;
  selectedBlock: Block | null;
  onUpdate: (blockId: string, updates: Partial<Block>) => void;
  onDelete: (blockId: string) => void;
  onDuplicate: (blockId: string) => void;
}

export interface CanvasProps {
  content: ContentState;
  selectedBlockId?: string | null;
  onRowAdd?: (index?: number) => void;
  onRowRemove?: (rowId: string) => void;
  onColumnAdd?: (rowId: string, size: number, index?: number) => void;
  onColumnRemove?: (columnId: string) => void;
  onBlockAdd?: (columnId: string, blockType: string, index?: number) => void;
  onBlockRemove?: (blockId: string) => void;
  onBlockUpdate?: (blockId: string, updates: Partial<Block>) => void;
  onBlockSelect?: (blockId: string | null) => void;
  onBlockMove?: (blockId: string, targetColumnId: string, targetIndex: number) => void;
  className?: string;
  viewport?: 'mobile' | 'tablet' | 'desktop';
  zoom?: number;
  getViewportWidth?: () => string;
}

// Custom block definition
export interface CustomBlock {
  type: BlockType;
  component: React.ComponentType<BlockRenderProps>;
  configComponent?: React.ComponentType<{ block: Block; onChange: (block: Block) => void }>;
}

// Block template
export interface BlockTemplate {
  name: string;
  description: string;
  thumbnail?: string;
  blocks: Block[];
}
