{"name": "@guti/content-builder-react", "version": "1.0.0", "description": "Framework-agnostic drag-and-drop content builder for React with TailwindCSS UI", "main": "dist/index.cjs", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "type": "module", "exports": {".": {"import": "./dist/index.esm.js", "require": "./dist/index.cjs", "types": "./dist/index.d.ts"}, "./styles": "./dist/styles.css"}, "files": ["dist", "README.md"], "sideEffects": ["*.css"], "scripts": {"dev": "vite", "build": "npm run build:lib && npm run build:types", "build:lib": "rollup -c", "build:types": "tsc --project tsconfig.app.json --emitDeclarationOnly --declaration --declarationDir dist/types", "build:demo": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "prepublishOnly": "npm run build"}, "keywords": ["react", "content-builder", "drag-drop", "typescript", "page-builder", "tailwindcss"], "author": "Your Name", "license": "MIT", "homepage": "https://github.com/your-org/content-builder-react#readme", "repository": {"type": "git", "url": "git+https://github.com/your-org/content-builder-react.git"}, "bugs": {"url": "https://github.com/your-org/content-builder-react/issues"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^9.0.0", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@tailwindcss/vite": "^4.1.8", "clsx": "^2.1.1", "immer": "^10.1.1", "lucide-react": "^0.513.0", "nanoid": "^5.1.5", "react": ">=16.8.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": ">=16.8.0", "react-window": "^1.8.11", "tailwind-merge": "^3.3.0", "tailwindcss": "^4.1.8", "uuid": "^11.1.0", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/js": "^9.25.0", "@rollup/plugin-commonjs": "^28.0.3", "@rollup/plugin-node-resolve": "^16.0.1", "@rollup/plugin-typescript": "^12.1.2", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@types/uuid": "^10.0.0", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "jsdom": "^26.1.0", "rollup": "^4.42.0", "rollup-plugin-dts": "^6.2.1", "rollup-plugin-peer-deps-external": "^2.2.4", "rollup-plugin-postcss": "^4.0.2", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5", "vitest": "^3.2.3"}}